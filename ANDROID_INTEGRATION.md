# Android Integration Guide for SRSR Backend API

## Network Configuration

### 1. API Base URL for Android Emulator
```java
// Use this base URL in your Android app when testing with emulator
private static final String BASE_URL = "http://********:3000/api";
```

### 2. API Base URL for Physical Device
```java
// Replace YOUR_COMPUTER_IP with your actual IP address
private static final String BASE_URL = "http://YOUR_COMPUTER_IP:3000/api";
```

## Android Network Setup

### 1. Add Internet Permission
Add to your `AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 2. Allow HTTP Traffic (for development)
Add to your `AndroidManifest.xml` application tag:
```xml
<application
    android:usesCleartextTraffic="true"
    ... >
```

## HTTP Client Implementation

### Option 1: Using OkHttp + Gson

#### Dependencies (build.gradle)
```gradle
implementation 'com.squareup.okhttp3:okhttp:4.12.0'
implementation 'com.google.code.gson:gson:2.10.1'
```

#### API Client Class
```java
public class SRSRApiClient {
    private static final String BASE_URL = "http://********:3000/api";
    private OkHttpClient client;
    private Gson gson;
    private String sessionCookie;

    public SRSRApiClient() {
        this.client = new OkHttpClient();
        this.gson = new Gson();
    }

    // Login method
    public void login(String username, String password, ApiCallback<LoginResponse> callback) {
        LoginRequest loginRequest = new LoginRequest(username, password);
        String json = gson.toJson(loginRequest);

        RequestBody body = RequestBody.create(
            MediaType.parse("application/json"), json);

        Request request = new Request.Builder()
            .url(BASE_URL + "/auth/login")
            .post(body)
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    LoginResponse loginResponse = gson.fromJson(responseBody, LoginResponse.class);
                    
                    // Store session cookie
                    String setCookie = response.header("Set-Cookie");
                    if (setCookie != null) {
                        sessionCookie = setCookie.split(";")[0];
                    }
                    
                    callback.onSuccess(loginResponse);
                } else {
                    callback.onError("Login failed: " + response.code());
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    // Get sites method
    public void getSites(ApiCallback<SitesResponse> callback) {
        Request.Builder requestBuilder = new Request.Builder()
            .url(BASE_URL + "/sites")
            .get();

        // Add session cookie if available
        if (sessionCookie != null) {
            requestBuilder.addHeader("Cookie", sessionCookie);
        }

        Request request = requestBuilder.build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    SitesResponse sitesResponse = gson.fromJson(responseBody, SitesResponse.class);
                    callback.onSuccess(sitesResponse);
                } else {
                    callback.onError("Failed to get sites: " + response.code());
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }
}
```

### Option 2: Using Retrofit

#### Dependencies (build.gradle)
```gradle
implementation 'com.squareup.retrofit2:retrofit:2.9.0'
implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
```

#### API Interface
```java
public interface SRSRApiService {
    @POST("auth/login")
    Call<LoginResponse> login(@Body LoginRequest request);

    @GET("sites")
    Call<SitesResponse> getSites();

    @POST("sites")
    Call<SiteResponse> createSite(@Body CreateSiteRequest request);

    @GET("maintenance-issues")
    Call<MaintenanceIssuesResponse> getMaintenanceIssues(
        @Query("property_id") String propertyId,
        @Query("status") String status,
        @Query("limit") Integer limit
    );
}
```

#### Retrofit Client Setup
```java
public class ApiClient {
    private static final String BASE_URL = "http://********:3000/api/";
    private static Retrofit retrofit;
    private static SRSRApiService apiService;

    public static SRSRApiService getApiService() {
        if (apiService == null) {
            OkHttpClient.Builder httpClient = new OkHttpClient.Builder();
            
            // Add logging interceptor for debugging
            HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
            logging.setLevel(HttpLoggingInterceptor.Level.BODY);
            httpClient.addInterceptor(logging);

            retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .client(httpClient.build())
                .build();

            apiService = retrofit.create(SRSRApiService.class);
        }
        return apiService;
    }
}
```

## Data Models

### Response Models
```java
public class LoginResponse {
    public boolean success;
    public User user;
    public String token;
    public String error;
}

public class User {
    public String id;
    public String username;
    public String fullName;
    public String email;
    public String role;
}

public class SitesResponse {
    public boolean success;
    public List<Site> data;
    public String error;
}

public class Site {
    public String id;
    public String name;
    public String location;
    public String created_at;
    public String updated_at;
}
```

### Request Models
```java
public class LoginRequest {
    public String username;
    public String password;

    public LoginRequest(String username, String password) {
        this.username = username;
        this.password = password;
    }
}

public class CreateSiteRequest {
    public String name;
    public String location;

    public CreateSiteRequest(String name, String location) {
        this.name = name;
        this.location = location;
    }
}
```

## Usage in Activity/Fragment

```java
public class MainActivity extends AppCompatActivity {
    private SRSRApiClient apiClient;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        apiClient = new SRSRApiClient();

        // Login
        apiClient.login("admin", "admin123", new ApiCallback<LoginResponse>() {
            @Override
            public void onSuccess(LoginResponse response) {
                runOnUiThread(() -> {
                    if (response.success) {
                        Toast.makeText(MainActivity.this, 
                            "Login successful: " + response.user.username, 
                            Toast.LENGTH_SHORT).show();
                        loadSites();
                    } else {
                        Toast.makeText(MainActivity.this, 
                            "Login failed: " + response.error, 
                            Toast.LENGTH_SHORT).show();
                    }
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, 
                        "Error: " + error, 
                        Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private void loadSites() {
        apiClient.getSites(new ApiCallback<SitesResponse>() {
            @Override
            public void onSuccess(SitesResponse response) {
                runOnUiThread(() -> {
                    if (response.success) {
                        // Update UI with sites data
                        updateSitesList(response.data);
                    }
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, 
                        "Failed to load sites: " + error, 
                        Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
}
```

## Testing Steps

1. **Start the backend server:**
   ```bash
   npm run dev
   ```

2. **Test API accessibility:**
   ```bash
   node test-android-api.js
   ```

3. **In Android Studio:**
   - Create new project or open existing
   - Add network permissions
   - Implement API client
   - Test on emulator

## Troubleshooting

### Common Issues:

1. **Connection Refused:**
   - Ensure server is running with `-H 0.0.0.0`
   - Check firewall settings
   - Try using computer's IP instead of ********

2. **Network Security Policy (Android 9+):**
   - Add `android:usesCleartextTraffic="true"` to manifest
   - Or configure network security config for HTTPS

3. **CORS Issues:**
   - Not typically an issue for mobile apps
   - Only affects web browsers

4. **Authentication:**
   - Store and send session cookies with requests
   - Handle token expiration gracefully

## Production Considerations

1. **Use HTTPS in production**
2. **Implement proper certificate pinning**
3. **Add request/response encryption**
4. **Implement proper error handling**
5. **Add offline capability with local database**
