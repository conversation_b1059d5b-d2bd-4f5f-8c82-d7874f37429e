import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createSiteSchema = z.object({
  name: z.string().min(1).max(255),
  location: z.string().min(1).max(255),
})

// GET /api/sites - Get all sites
export async function GET() {
  try {
    const sites = await queryMany(
      'SELECT id, name, location, created_at, updated_at FROM sites ORDER BY name'
    )

    return NextResponse.json({
      success: true,
      data: sites,
    })

  } catch (error) {
    console.error('Error fetching sites:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sites' },
      { status: 500 }
    )
  }
}

// POST /api/sites - Create a new site
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createSiteSchema.parse(body)
    const { name, location } = validatedData

    // Insert new site
    const newSite = await queryInsert(
      `INSERT INTO sites (name, location) 
       VALUES ($1, $2) 
       RETURNING id, name, location, created_at, updated_at`,
      [name, location]
    )

    return NextResponse.json({
      success: true,
      data: newSite,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating site:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create site' },
      { status: 500 }
    )
  }
}
