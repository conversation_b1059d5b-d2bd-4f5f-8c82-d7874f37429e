"use client"

import { useUser<PERSON>ole } from "@/hooks/use-user-role"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function RoleDebugger() {
  const { user, roles, loading, isAdmin } = useUserRole()

  if (loading) {
    return <div>Loading user role information...</div>
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>User Role Debug Information</CardTitle>
        <CardDescription>This component helps troubleshoot role-based access issues</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium">User Information:</h3>
            {user ? (
              <div className="mt-2 rounded-md bg-slate-50 p-3">
                <p>
                  <strong>Username:</strong> {user.username}
                </p>
                <p>
                  <strong>User Role:</strong> {user.role}
                </p>
                <p>
                  <strong>User ID:</strong> {user.id}
                </p>
                <p>
                  <strong>Is Active:</strong> {user.is_active ? "Yes" : "No"}
                </p>
              </div>
            ) : (
              <p className="text-red-500">No user found</p>
            )}
          </div>

          <div>
            <h3 className="font-medium">Assigned Roles:</h3>
            {roles.length > 0 ? (
              <div className="mt-2 flex flex-wrap gap-2">
                {roles.map((role) => (
                  <Badge key={role.id} variant="outline">
                    {role.name}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-amber-500">No roles assigned</p>
            )}
          </div>

          <div>
            <h3 className="font-medium">Admin Status:</h3>
            <p className={isAdmin ? "text-green-500" : "text-red-500"}>
              {isAdmin ? "User has admin privileges" : "User does not have admin privileges"}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
