const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Database connection configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'srsr_backend',
  password: process.env.DB_PASSWORD || 'password',
  port: parseInt(process.env.DB_PORT || '5432'),
});

async function seedDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('Starting database seeding...');
    
    // Create default roles
    await client.query(`
      INSERT INTO roles (name, description) VALUES 
      ('admin', 'Administrator with full access'),
      ('manager', 'Manager with limited administrative access'),
      ('user', 'Regular user with basic access'),
      ('viewer', 'Read-only access')
      ON CONFLICT (name) DO NOTHING;
    `);
    
    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 10);
    await client.query(`
      INSERT INTO users (username, password, email, full_name, role, is_active) VALUES 
      ('admin', $1, '<EMAIL>', 'System Administrator', 'admin', true)
      ON CONFLICT (username) DO NOTHING;
    `, [hashedPassword]);
    
    // Insert default sites
    await client.query(`
      INSERT INTO sites (id, name, location) VALUES 
      ('f47ac10b-58cc-4372-a567-0e02b2c3d479', 'Gandipet 1', 'Gandipet Location 1'),
      ('f47ac10b-58cc-4372-a567-0e02b2c3d480', 'Gandipet 2', 'Gandipet Location 2'),
      ('f47ac10b-58cc-4372-a567-0e02b2c3d481', 'Gandipet 3 & 4', 'Gandipet Location 3 & 4'),
      ('f47ac10b-58cc-4372-a567-0e02b2c3d482', 'Bachupally', 'Bachupally Location')
      ON CONFLICT (id) DO NOTHING;
    `);
    
    // Insert default offices
    await client.query(`
      INSERT INTO offices (id, name, location) VALUES 
      ('f47ac10b-58cc-4372-a567-0e02b2c3d483', 'Back Office - Brane', 'Back Office - Brane'),
      ('f47ac10b-58cc-4372-a567-0e02b2c3d484', 'STRF Office - Brane', 'STRF Office - Brane'),
      ('f47ac10b-58cc-4372-a567-0e02b2c3d485', 'Road No. 36 office - SRSR', 'Road No. 36 office - SRSR'),
      ('f47ac10b-58cc-4372-a567-0e02b2c3d486', 'Back Office - SRSR', 'Back Office - SRSR')
      ON CONFLICT (id) DO NOTHING;
    `);
    
    // Insert default threshold configurations
    await client.query(`
      INSERT INTO threshold_config (functional_area, metric_name, green_min, green_max, orange_min, orange_max, red_min, red_max, unit, description, property_type) VALUES 
      ('electricity', 'generator_fuel_percentage', 50, 100, 20, 49.99, 0, 19.99, '%', 'Generator fuel level percentage', 'all'),
      ('electricity', 'backup_hours', 12, 999, 6, 11.99, 0, 5.99, 'hours', 'Backup power available in hours', 'all'),
      ('water', 'tank_level', 70, 100, 30, 69.99, 0, 29.99, '%', 'Water tank level percentage', 'all'),
      ('security', 'camera_uptime', 95, 100, 80, 94.99, 0, 79.99, '%', 'Security camera uptime percentage', 'all'),
      ('internet', 'connection_speed', 50, 999, 25, 49.99, 0, 24.99, 'Mbps', 'Internet connection speed', 'all'),
      ('maintenance', 'open_issues', 0, 2, 3, 5, 6, 999, 'count', 'Number of open maintenance issues', 'all'),
      ('attendance', 'attendance_percentage', 90, 100, 70, 89.99, 0, 69.99, '%', 'Staff attendance percentage', 'all')
      ON CONFLICT DO NOTHING;
    `);
    
    // Insert default permissions
    const roleResult = await client.query('SELECT id FROM roles WHERE name = $1', ['admin']);
    if (roleResult.rows.length > 0) {
      const adminRoleId = roleResult.rows[0].id;
      await client.query(`
        INSERT INTO permissions (url_pattern, role_id) VALUES 
        ('/api/*', $1),
        ('/admin/*', $1)
        ON CONFLICT DO NOTHING;
      `, [adminRoleId]);
    }
    
    console.log('Database seeding completed successfully!');
    
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
