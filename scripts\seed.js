const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Database connection configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'srsr',
  password: process.env.DB_PASSWORD || 'pg123',
  port: parseInt(process.env.DB_PORT || '5432'),
});

async function seedDatabase() {
  const client = await pool.connect();

  try {
    console.log('Starting database seeding...');

    // Insert Roles
    await client.query(`
      INSERT INTO roles (id, name, description) VALUES
      (1, 'admin', 'Full system access'),
      (2, 'manager', 'Property and office management'),
      (3, 'user', 'Limited property access'),
      (4, 'guest', 'Read-only access')
      ON CONFLICT (id) DO NOTHING;
    `);

    // Insert Sample Users
    const hashedPassword = await bcrypt.hash('admin123', 10);
    await client.query(`
      INSERT INTO users (id, username, password, full_name, email, role, is_active) VALUES
      ('550e8400-e29b-41d4-a716-446655440000', 'admin', $1, 'System Administrator', '<EMAIL>', 'admin', true),
      ('550e8400-e29b-41d4-a716-446655440001', 'manager1', $1, 'Property Manager', '<EMAIL>', 'manager', true),
      ('550e8400-e29b-41d4-a716-446655440002', 'user1', $1, 'Site User', '<EMAIL>', 'user', true)
      ON CONFLICT (id) DO NOTHING;
    `, [hashedPassword]);

    // Insert Sites
    await client.query(`
      INSERT INTO sites (id, name, location) VALUES
      ('550e8400-e29b-41d4-a716-446655440001', 'Jublee Hills Site', 'Jublee Hills, Hyderabad'),
      ('550e8400-e29b-41d4-a716-446655440002', 'Gandipet Site', 'Gandipet, Hyderabad'),
      ('550e8400-e29b-41d4-a716-446655440003', 'Banjara Hills Site', 'Banjara Hills, Hyderabad'),
      ('550e8400-e29b-41d4-a716-446655440004', 'Gachibowli Site', 'Gachibowli, Hyderabad')
      ON CONFLICT (id) DO NOTHING;
    `);

    // Insert Offices
    await client.query(`
      INSERT INTO offices (id, name, location) VALUES
      ('550e8400-e29b-41d4-a716-446655440011', 'Main Office', 'Hyderabad Central'),
      ('550e8400-e29b-41d4-a716-446655440012', 'Branch Office', 'Secunderabad')
      ON CONFLICT (id) DO NOTHING;
    `);

    // Insert Escalation Config
    await client.query(`
      INSERT INTO escalation_config (priority, escalation_level, days_to_escalate, escalate_to) VALUES
      ('High', 1, 1, 'Site Manager'),
      ('High', 2, 2, 'Regional Manager'),
      ('High', 3, 3, 'Operations Head'),
      ('Medium', 1, 3, 'Site Manager'),
      ('Medium', 2, 7, 'Regional Manager'),
      ('Low', 1, 7, 'Site Manager'),
      ('Low', 2, 14, 'Regional Manager')
      ON CONFLICT (priority, escalation_level) DO NOTHING;
    `);

    // Insert Generator Fuel Updates
    await client.query(`
      INSERT INTO generator_fuel_updates (property_id, date, starting_reading, ending_reading, fuel_in_generator_percentage, fuel_in_tank_liters) VALUES
      ('jublee-hills', '2025-01-26', 1250.5, 1275.2, 85, 450.0),
      ('jublee-hills', '2025-01-25', 1225.0, 1250.5, 90, 480.0),
      ('jublee-hills', '2025-01-24', 1200.0, 1225.0, 95, 500.0),
      ('gandipet-guest-house', '2025-01-26', 850.0, 875.5, 75, 350.0),
      ('gandipet-guest-house', '2025-01-25', 825.0, 850.0, 80, 380.0)
      ON CONFLICT DO NOTHING;
    `);

    // Insert Diesel Additions
    await client.query(`
      INSERT INTO diesel_additions (property_id, service_id, date, diesel_added, added_by) VALUES
      ('jublee-hills', 'generator', '2025-01-26', 100.0, 'Maintenance Team'),
      ('jublee-hills', 'generator', '2025-01-24', 150.0, 'Fuel Supplier'),
      ('gandipet-guest-house', 'generator', '2025-01-25', 120.0, 'Site Manager')
      ON CONFLICT DO NOTHING;
    `);

    // Insert Maintenance Issues
    await client.query(`
      INSERT INTO maintenance_issues (id, property_id, issue_type, category, issue_date, status, reported_by, priority) VALUES
      ('550e8400-e29b-41d4-a716-446655440100', 'jublee-hills', 'Generator Maintenance', 'Electrical', '2025-01-25', 'Open', 'Site Manager', 'High'),
      ('550e8400-e29b-41d4-a716-446655440101', 'jublee-hills', 'CCTV Camera Issue', 'Security', '2025-01-24', 'In Progress', 'Security Team', 'Medium'),
      ('550e8400-e29b-41d4-a716-446655440102', 'gandipet-guest-house', 'Internet Connectivity', 'Network', '2025-01-23', 'Resolved', 'IT Team', 'High')
      ON CONFLICT (id) DO NOTHING;
    `);

    // Insert OTT Services
    await client.query(`
      INSERT INTO ott_services (id, property_id, service_name, provider, subscription_type, monthly_cost, start_date, expiry_date, status) VALUES
      ('550e8400-e29b-41d4-a716-446655440200', 'jublee-hills', 'Netflix Premium', 'Netflix', 'Premium', 649.00, '2024-12-01', '2025-11-30', 'Active'),
      ('550e8400-e29b-41d4-a716-446655440201', 'jublee-hills', 'Amazon Prime Video', 'Amazon', 'Annual', 1499.00, '2024-06-01', '2025-05-31', 'Active'),
      ('550e8400-e29b-41d4-a716-446655440202', 'gandipet-guest-house', 'Disney+ Hotstar', 'Disney', 'Super', 899.00, '2024-08-01', '2025-07-31', 'Active')
      ON CONFLICT (id) DO NOTHING;
    `);

    // Insert Threshold Configurations
    await client.query(`
      INSERT INTO threshold_configurations (functional_area, metric_name, green_min, green_max, orange_min, orange_max, red_min, red_max, unit, description) VALUES
      ('Generator', 'Fuel Level', 80.0, 100.0, 50.0, 79.9, 0.0, 49.9, '%', 'Generator fuel level percentage'),
      ('Security', 'Camera Status', 95.0, 100.0, 80.0, 94.9, 0.0, 79.9, '%', 'Percentage of working cameras'),
      ('Network', 'Uptime', 95.0, 100.0, 90.0, 94.9, 0.0, 89.9, '%', 'Network uptime percentage'),
      ('Maintenance', 'Open Issues', 0.0, 2.0, 3.0, 5.0, 6.0, 999.0, 'count', 'Number of open maintenance issues')
      ON CONFLICT (functional_area, metric_name, property_type) DO NOTHING;
    `);

    // Insert Permissions
    await client.query(`
      INSERT INTO permissions (url_pattern, role_id) VALUES
      ('/admin%', 1),
      ('/dashboard%', 2),
      ('/dashboard%', 3),
      ('/dashboard/home%', 3),
      ('/dashboard/office%', 2),
      ('/dashboard/status%', 2),
      ('/dashboard/status%', 3)
      ON CONFLICT DO NOTHING;
    `);

    // Insert User Roles Assignments
    await client.query(`
      INSERT INTO user_roles (user_id, role_id) VALUES
      ('550e8400-e29b-41d4-a716-446655440000', 1),
      ('550e8400-e29b-41d4-a716-446655440001', 2),
      ('550e8400-e29b-41d4-a716-446655440002', 3)
      ON CONFLICT (user_id, role_id) DO NOTHING;
    `);

    console.log('Database seeding completed successfully!');

  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
