-- =====================================================
-- PROPERTY MANAGEMENT SYSTEM - COMPLETE DATABASE DUMP
-- Generated: January 2025
-- =====================================================

-- Drop existing tables if they exist (in correct order to handle dependencies)
DROP TABLE IF EXISTS escalation_matrix CASCADE;
DROP TABLE IF EXISTS escalation_config CASCADE;
DROP TABLE IF EXISTS site_attendance CASCADE;
DROP TABLE IF EXISTS site_members CASCADE;
DROP TABLE IF EXISTS office_attendance CASCADE;
DROP TABLE IF EXISTS office_members CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS generator_fuel_updates CASCADE;
DROP TABLE IF EXISTS diesel_additions CASCADE;
DROP TABLE IF EXISTS maintenance_issues CASCADE;
DROP TABLE IF EXISTS ott_services CASCADE;
DROP TABLE IF EXISTS sites CASCADE;
DROP TABLE IF EXISTS offices CASCADE;
DROP TABLE IF EXISTS function_process_matrix CASCADE;
DROP TABLE IF EXISTS threshold_configurations CASCADE;
DROP TABLE IF EXISTS dashboard_widgets CASCADE;
DROP TABLE IF EXISTS network_disruption_history CASCADE;

-- =====================================================
-- TABLE CREATION WITH COMPLETE STRUCTURE
-- =====================================================

-- 1. USERS TABLE
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  username VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  role VARCHAR(50) DEFAULT 'user',
  is_active BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. ROLES TABLE
CREATE TABLE roles (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) UNIQUE NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. USER_ROLES TABLE
CREATE TABLE user_roles (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, role_id)
);

-- 4. PERMISSIONS TABLE
CREATE TABLE permissions (
  id SERIAL PRIMARY KEY,
  url_pattern VARCHAR(255) NOT NULL,
  role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. SITES TABLE
CREATE TABLE sites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  location VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. OFFICES TABLE
CREATE TABLE offices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  location VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. GENERATOR FUEL UPDATES TABLE
CREATE TABLE generator_fuel_updates (
  id SERIAL PRIMARY KEY,
  property_id VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  starting_reading DECIMAL(10,2) NOT NULL,
  ending_reading DECIMAL(10,2) NOT NULL,
  fuel_in_generator_percentage INTEGER NOT NULL,
  fuel_in_tank_liters DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. DIESEL ADDITIONS TABLE
CREATE TABLE diesel_additions (
  id SERIAL PRIMARY KEY,
  property_id TEXT NOT NULL,
  service_id TEXT NOT NULL,
  date DATE NOT NULL,
  diesel_added NUMERIC(10,2) NOT NULL,
  added_by TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. MAINTENANCE ISSUES TABLE
CREATE TABLE maintenance_issues (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id VARCHAR(255) NOT NULL,
  issue_type VARCHAR(255) NOT NULL,
  category VARCHAR(255) NOT NULL,
  issue_date DATE NOT NULL,
  status VARCHAR(50) DEFAULT 'Open',
  resolution_date DATE,
  remarks TEXT,
  reported_by VARCHAR(255) NOT NULL,
  assigned_to VARCHAR(255),
  priority VARCHAR(50) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. ESCALATION CONFIG TABLE
CREATE TABLE escalation_config (
  id SERIAL PRIMARY KEY,
  priority VARCHAR(50) NOT NULL,
  escalation_level INTEGER NOT NULL,
  days_to_escalate INTEGER NOT NULL,
  escalate_to VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(priority, escalation_level)
);

-- 11. ESCALATION MATRIX TABLE
CREATE TABLE escalation_matrix (
  id SERIAL PRIMARY KEY,
  issue_id UUID REFERENCES maintenance_issues(id) ON DELETE CASCADE,
  escalation_level INTEGER NOT NULL,
  escalated_to VARCHAR(255) NOT NULL,
  escalated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved BOOLEAN DEFAULT false
);

-- 12. OTT SERVICES TABLE
CREATE TABLE ott_services (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id VARCHAR(255) NOT NULL,
  service_name VARCHAR(255) NOT NULL,
  provider VARCHAR(255) NOT NULL,
  subscription_type VARCHAR(100) NOT NULL,
  monthly_cost DECIMAL(10,2) NOT NULL,
  start_date DATE NOT NULL,
  expiry_date DATE NOT NULL,
  status VARCHAR(50) DEFAULT 'Active',
  auto_renewal BOOLEAN DEFAULT false,
  login_credentials TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 13. SITE MEMBERS TABLE
CREATE TABLE site_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  mobile_number VARCHAR(20) NOT NULL,
  site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
  role VARCHAR(100),
  duty_time VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 14. SITE ATTENDANCE TABLE
CREATE TABLE site_attendance (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
  worker_id VARCHAR(255) NOT NULL,
  worker_name VARCHAR(255) NOT NULL,
  worker_role VARCHAR(100) NOT NULL,
  date DATE NOT NULL,
  status VARCHAR(50) NOT NULL,
  hours_worked DECIMAL(4,2) DEFAULT 0,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(site_id, worker_id, date)
);

-- 15. OFFICE MEMBERS TABLE
CREATE TABLE office_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  mobile_number VARCHAR(20) NOT NULL,
  office_location VARCHAR(255) NOT NULL,
  role VARCHAR(100),
  duty_time VARCHAR(100),
  remarks TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 16. OFFICE ATTENDANCE TABLE
CREATE TABLE office_attendance (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  member_id UUID REFERENCES office_members(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  status VARCHAR(50) NOT NULL,
  check_in TIME,
  check_out TIME,
  hours_worked DECIMAL(4,2),
  remarks TEXT,
  office_location VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(member_id, date)
);

-- 17. FUNCTION PROCESS MATRIX TABLE
CREATE TABLE function_process_matrix (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  function_name TEXT NOT NULL,
  sub_function TEXT NOT NULL,
  input TEXT NOT NULL,
  process TEXT NOT NULL,
  output TEXT NOT NULL,
  threshold_limits TEXT NOT NULL,
  responsible_agent TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 18. THRESHOLD CONFIGURATIONS TABLE
CREATE TABLE threshold_configurations (
  id SERIAL PRIMARY KEY,
  functional_area VARCHAR(100) NOT NULL,
  metric_name VARCHAR(100) NOT NULL,
  green_min DECIMAL(10,2),
  green_max DECIMAL(10,2),
  orange_min DECIMAL(10,2),
  orange_max DECIMAL(10,2),
  red_min DECIMAL(10,2),
  red_max DECIMAL(10,2),
  unit VARCHAR(20),
  description TEXT,
  property_type VARCHAR(50) DEFAULT 'all',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(functional_area, metric_name, property_type)
);

-- 19. DASHBOARD WIDGETS TABLE
CREATE TABLE dashboard_widgets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  widget_type VARCHAR(100) NOT NULL,
  role_required VARCHAR(100),
  display_order INTEGER DEFAULT 0,
  is_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 20. NETWORK DISRUPTION HISTORY TABLE
CREATE TABLE network_disruption_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id VARCHAR(255) NOT NULL,
  date_period VARCHAR(255) NOT NULL,
  downtime_duration VARCHAR(255),
  uptime_percentage VARCHAR(10),
  event_time_ist TEXT,
  reason_for_disruption TEXT,
  uptime_numeric DECIMAL(5,2),
  downtime_minutes INTEGER,
  disruption_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX idx_generator_fuel_property_date ON generator_fuel_updates(property_id, date DESC);
CREATE INDEX idx_maintenance_property_status ON maintenance_issues(property_id, status);
CREATE INDEX idx_maintenance_date ON maintenance_issues(issue_date DESC);
CREATE INDEX idx_ott_property_status ON ott_services(property_id, status);
CREATE INDEX idx_ott_expiry ON ott_services(expiry_date);
CREATE INDEX idx_site_attendance_site_date ON site_attendance(site_id, date DESC);
CREATE INDEX idx_office_attendance_member_date ON office_attendance(member_id, date DESC);
CREATE INDEX idx_network_disruption_property_period ON network_disruption_history(property_id, date_period);
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_permissions_role ON permissions(role_id);

-- =====================================================
-- DATA INSERTS
-- =====================================================

-- Insert Roles
INSERT INTO roles (id, name, description) VALUES 
(1, 'admin', 'Full system access'),
(2, 'manager', 'Property and office management'),
(3, 'user', 'Limited property access'),
(4, 'guest', 'Read-only access');

-- Insert Sample Users
INSERT INTO users (id, username, password, full_name, email, role, is_active) VALUES 
('550e8400-e29b-41d4-a716-446655440000', 'admin', '$2b$10$hashedpassword', 'System Administrator', '<EMAIL>', 'admin', true),
('550e8400-e29b-41d4-a716-446655440001', 'manager1', '$2b$10$hashedpassword', 'Property Manager', '<EMAIL>', 'manager', true),
('550e8400-e29b-41d4-a716-446655440002', 'user1', '$2b$10$hashedpassword', 'Site User', '<EMAIL>', 'user', true);

-- Insert Sites
INSERT INTO sites (id, name, location) VALUES 
('550e8400-e29b-41d4-a716-446655440001', 'Jublee Hills Site', 'Jublee Hills, Hyderabad'),
('550e8400-e29b-41d4-a716-446655440002', 'Gandipet Site', 'Gandipet, Hyderabad'),
('550e8400-e29b-41d4-a716-446655440003', 'Banjara Hills Site', 'Banjara Hills, Hyderabad'),
('550e8400-e29b-41d4-a716-446655440004', 'Gachibowli Site', 'Gachibowli, Hyderabad');

-- Insert Offices
INSERT INTO offices (id, name, location) VALUES 
('550e8400-e29b-41d4-a716-446655440011', 'Main Office', 'Hyderabad Central'),
('550e8400-e29b-41d4-a716-446655440012', 'Branch Office', 'Secunderabad');

-- Insert Escalation Config
INSERT INTO escalation_config (priority, escalation_level, days_to_escalate, escalate_to) VALUES 
('High', 1, 1, 'Site Manager'),
('High', 2, 2, 'Regional Manager'),
('High', 3, 3, 'Operations Head'),
('Medium', 1, 3, 'Site Manager'),
('Medium', 2, 7, 'Regional Manager'),
('Low', 1, 7, 'Site Manager'),
('Low', 2, 14, 'Regional Manager');

-- Insert Generator Fuel Updates
INSERT INTO generator_fuel_updates (property_id, date, starting_reading, ending_reading, fuel_in_generator_percentage, fuel_in_tank_liters) VALUES 
('jublee-hills', '2025-01-26', 1250.5, 1275.2, 85, 450.0),
('jublee-hills', '2025-01-25', 1225.0, 1250.5, 90, 480.0),
('jublee-hills', '2025-01-24', 1200.0, 1225.0, 95, 500.0),
('gandipet-guest-house', '2025-01-26', 850.0, 875.5, 75, 350.0),
('gandipet-guest-house', '2025-01-25', 825.0, 850.0, 80, 380.0);

-- Insert Diesel Additions
INSERT INTO diesel_additions (property_id, service_id, date, diesel_added, added_by) VALUES 
('jublee-hills', 'generator', '2025-01-26', 100.0, 'Maintenance Team'),
('jublee-hills', 'generator', '2025-01-24', 150.0, 'Fuel Supplier'),
('gandipet-guest-house', 'generator', '2025-01-25', 120.0, 'Site Manager');

-- Insert Maintenance Issues
INSERT INTO maintenance_issues (id, property_id, issue_type, category, issue_date, status, reported_by, priority) VALUES 
('550e8400-e29b-41d4-a716-446655440100', 'jublee-hills', 'Generator Maintenance', 'Electrical', '2025-01-25', 'Open', 'Site Manager', 'High'),
('550e8400-e29b-41d4-a716-446655440101', 'jublee-hills', 'CCTV Camera Issue', 'Security', '2025-01-24', 'In Progress', 'Security Team', 'Medium'),
('550e8400-e29b-41d4-a716-446655440102', 'gandipet-guest-house', 'Internet Connectivity', 'Network', '2025-01-23', 'Resolved', 'IT Team', 'High');

-- Insert OTT Services
INSERT INTO ott_services (id, property_id, service_name, provider, subscription_type, monthly_cost, start_date, expiry_date, status) VALUES 
('550e8400-e29b-41d4-a716-446655440200', 'jublee-hills', 'Netflix Premium', 'Netflix', 'Premium', 649.00, '2024-12-01', '2025-11-30', 'Active'),
('550e8400-e29b-41d4-a716-446655440201', 'jublee-hills', 'Amazon Prime Video', 'Amazon', 'Annual', 1499.00, '2024-06-01', '2025-05-31', 'Active'),
('550e8400-e29b-41d4-a716-446655440202', 'gandipet-guest-house', 'Disney+ Hotstar', 'Disney', 'Super', 899.00, '2024-08-01', '2025-07-31', 'Active');

-- Insert Site Members
INSERT INTO site_members (id, name, mobile_number, site_id, role, duty_time) VALUES 
('550e8400-e29b-41d4-a716-446655440300', 'Rajesh Kumar', '+91-**********', '550e8400-e29b-41d4-a716-446655440001', 'Security Guard', '6 AM - 6 PM'),
('550e8400-e29b-41d4-a716-446655440301', 'Suresh Reddy', '+91-**********', '550e8400-e29b-41d4-a716-446655440001', 'Maintenance', '9 AM - 5 PM'),
('550e8400-e29b-41d4-a716-446655440302', 'Venkat Rao', '+91-**********', '550e8400-e29b-41d4-a716-446655440002', 'Security Guard', '6 PM - 6 AM');

-- Insert Office Members
INSERT INTO office_members (id, name, mobile_number, office_location, role, duty_time) VALUES 
('550e8400-e29b-41d4-a716-446655440400', 'Priya Sharma', '+91-9876543220', 'Hyderabad Central', 'Manager', '9 AM - 6 PM'),
('550e8400-e29b-41d4-a716-446655440401', 'Amit Singh', '+91-9876543221', 'Hyderabad Central', 'Executive', '9 AM - 6 PM'),
('550e8400-e29b-41d4-a716-446655440402', 'Kavitha Reddy', '+91-9876543222', 'Secunderabad', 'Assistant Manager', '10 AM - 7 PM');

-- Insert Site Attendance
INSERT INTO site_attendance (site_id, worker_id, worker_name, worker_role, date, status, hours_worked) VALUES 
('550e8400-e29b-41d4-a716-446655440001', 'W001', 'Rajesh Kumar', 'Security Guard', '2025-01-26', 'Present', 12.0),
('550e8400-e29b-41d4-a716-446655440001', 'W002', 'Suresh Reddy', 'Maintenance', '2025-01-26', 'Present', 8.0),
('550e8400-e29b-41d4-a716-446655440002', 'W003', 'Venkat Rao', 'Security Guard', '2025-01-26', 'Present', 12.0);

-- Insert Function Process Matrix
INSERT INTO function_process_matrix (function_name, sub_function, input, process, output, threshold_limits, responsible_agent) VALUES 
('Generator Management', 'Fuel Monitoring', 'Daily fuel readings', 'Monitor fuel levels and consumption', 'Fuel status report', 'Green: >80%, Orange: 50-80%, Red: <50%', 'Site Maintenance Team'),
('Security Management', 'CCTV Monitoring', 'Camera feeds', 'Monitor all camera feeds for security', 'Security status report', 'Green: All cameras working, Orange: 1-2 cameras down, Red: >2 cameras down', 'Security Team'),
('Network Management', 'Internet Connectivity', 'Network status', 'Monitor internet uptime and speed', 'Network status report', 'Green: >95% uptime, Orange: 90-95%, Red: <90%', 'IT Team');

-- Insert Threshold Configurations
INSERT INTO threshold_configurations (functional_area, metric_name, green_min, green_max, orange_min, orange_max, red_min, red_max, unit, description) VALUES 
('Generator', 'Fuel Level', 80.0, 100.0, 50.0, 79.9, 0.0, 49.9, '%', 'Generator fuel level percentage'),
('Security', 'Camera Status', 95.0, 100.0, 80.0, 94.9, 0.0, 79.9, '%', 'Percentage of working cameras'),
('Network', 'Uptime', 95.0, 100.0, 90.0, 94.9, 0.0, 89.9, '%', 'Network uptime percentage'),
('Maintenance', 'Open Issues', 0.0, 2.0, 3.0, 5.0, 6.0, 999.0, 'count', 'Number of open maintenance issues');

-- Insert Dashboard Widgets
INSERT INTO dashboard_widgets (title, widget_type, role_required, display_order, is_enabled) VALUES 
('Current Statuses', 'status_overview', 'user', 1, true),
('Generator Fuel Levels', 'fuel_status', 'user', 2, true),
('Maintenance Issues', 'maintenance_status', 'user', 3, true),
('Security Status', 'security_status', 'user', 4, true),
('Network Status', 'network_status', 'user', 5, true),
('User Management', 'user_admin', 'admin', 6, true);

-- Insert Network Disruption History (CSV Data)
INSERT INTO network_disruption_history (property_id, date_period, downtime_duration, uptime_percentage, event_time_ist, reason_for_disruption, uptime_numeric, downtime_minutes, disruption_count) VALUES 
('jublee-hills', 'Monthly Avg', '4 hrs 31 mins', '98.68%', '04:52 AM – 06:52 AM; 11:28 AM – 01:59 PM', 'external - Disruption from service provider (Two disruptions)', 98.68, 271, 2),
('jublee-hills', '01-05-2025', '0 mins', '100%', 'No disruptions', 'No issues reported', 100.00, 0, 0),
('jublee-hills', '02-05-2025', '2 hrs 1 min', '91.60%', '04:52 AM – 06:52 AM', 'external - Disruption from service provider', 91.60, 121, 1),
('jublee-hills', '30-05-2025', '2 hrs 30 mins', '89.58%', '11:28 AM – 01:59 PM', 'external - Disruption from service provider', 89.58, 150, 1);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_generator_fuel_updated_at BEFORE UPDATE ON generator_fuel_updates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_diesel_additions_updated_at BEFORE UPDATE ON diesel_additions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_issues_updated_at BEFORE UPDATE ON maintenance_issues FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_escalation_config_updated_at BEFORE UPDATE ON escalation_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ott_services_updated_at BEFORE UPDATE ON ott_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sites_updated_at BEFORE UPDATE ON sites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_site_members_updated_at BEFORE UPDATE ON site_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_site_attendance_updated_at BEFORE UPDATE ON site_attendance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offices_updated_at BEFORE UPDATE ON offices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_office_members_updated_at BEFORE UPDATE ON office_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_office_attendance_updated_at BEFORE UPDATE ON office_attendance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_function_process_matrix_updated_at BEFORE UPDATE ON function_process_matrix FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_threshold_configurations_updated_at BEFORE UPDATE ON threshold_configurations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dashboard_widgets_updated_at BEFORE UPDATE ON dashboard_widgets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_network_disruption_history_updated_at BEFORE UPDATE ON network_disruption_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- PERMISSIONS DATA
-- =====================================================

INSERT INTO permissions (url_pattern, role_id) VALUES 
('/admin%', 1),
('/dashboard%', 2),
('/dashboard%', 3),
('/dashboard/home%', 3),
('/dashboard/office%', 2),
('/dashboard/status%', 2),
('/dashboard/status%', 3);

-- =====================================================
-- USER ROLES ASSIGNMENTS
-- =====================================================

INSERT INTO user_roles (user_id, role_id) VALUES 
('550e8400-e29b-41d4-a716-446655440000', 1),
('550e8400-e29b-41d4-a716-446655440001', 2),
('550e8400-e29b-41d4-a716-446655440002', 3);

-- =====================================================
-- END OF DATABASE DUMP
-- =====================================================