"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Clock, AlertTriangle } from "lucide-react"
import { getBrowserClient } from "@/lib/supabase"
import { Badge } from "@/components/ui/badge"

interface EscalationMatrixProps {
  issueId: string
}

interface Escalation {
  id: number
  issue_id: string
  escalation_level: number
  escalated_to: string
  escalated_at: string
  resolved: boolean
}

export function EscalationMatrix({ issueId }: EscalationMatrixProps) {
  const [escalations, setEscalations] = useState<Escalation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const fetchEscalations = async () => {
      setLoading(true)
      try {
        const supabase = getBrowserClient()
        const { data, error } = await supabase
          .from("escalation_matrix")
          .select("*")
          .eq("issue_id", issueId)
          .order("escalation_level", { ascending: true })

        if (error) {
          setError("Failed to load escalation data")
          console.error(error)
          return
        }

        setEscalations(data || [])
      } catch (err) {
        setError("An unexpected error occurred")
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    if (issueId) {
      fetchEscalations()
    }
  }, [issueId])

  const formatDate = (dateString: string) => {
    if (!dateString) return "-"
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          Escalation Matrix
        </CardTitle>
        <CardDescription>Tracking of issue escalations</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center p-4">
            <Clock className="mr-2 h-4 w-4 animate-spin" />
            <p>Loading escalation data...</p>
          </div>
        ) : error ? (
          <div className="rounded-md bg-red-50 p-4 text-red-700">
            <p>{error}</p>
          </div>
        ) : escalations.length === 0 ? (
          <div className="rounded-md bg-slate-50 p-4 text-center">
            <p className="text-slate-500">No escalations found for this issue</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Level</TableHead>
                  <TableHead>Escalated To</TableHead>
                  <TableHead>Escalated At</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {escalations.map((escalation) => (
                  <TableRow key={escalation.id}>
                    <TableCell>{escalation.escalation_level}</TableCell>
                    <TableCell>{escalation.escalated_to}</TableCell>
                    <TableCell>{formatDate(escalation.escalated_at)}</TableCell>
                    <TableCell>
                      {escalation.resolved ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Resolved
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          Pending
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
