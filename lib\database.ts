import { Pool, PoolClient } from 'pg'

// Database connection configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'srsr_backend',
  password: process.env.DB_PASSWORD || 'password',
  port: parseInt(process.env.DB_PORT || '5432'),
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

// Database client wrapper
export class Database {
  private static instance: Database
  private pool: Pool

  private constructor() {
    this.pool = pool
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database()
    }
    return Database.instance
  }

  public async query(text: string, params?: any[]): Promise<any> {
    const client = await this.pool.connect()
    try {
      const result = await client.query(text, params)
      return result
    } finally {
      client.release()
    }
  }

  public async getClient(): Promise<PoolClient> {
    return await this.pool.connect()
  }

  public async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.pool.connect()
    try {
      await client.query('BEGIN')
      const result = await callback(client)
      await client.query('COMMIT')
      return result
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  public async close(): Promise<void> {
    await this.pool.end()
  }
}

// Export singleton instance
export const db = Database.getInstance()

// Helper function for common query patterns
export async function queryOne<T>(text: string, params?: any[]): Promise<T | null> {
  const result = await db.query(text, params)
  return result.rows[0] || null
}

export async function queryMany<T>(text: string, params?: any[]): Promise<T[]> {
  const result = await db.query(text, params)
  return result.rows
}

export async function queryInsert<T>(text: string, params?: any[]): Promise<T> {
  const result = await db.query(text, params)
  return result.rows[0]
}
