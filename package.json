{"name": "srsr-backend-api", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "dependencies": {"bcryptjs": "latest", "date-fns": "latest", "next": "15.2.4", "pg": "^8.11.3", "uuid": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "typescript": "^5"}}