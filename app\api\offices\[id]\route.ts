import { NextRequest, NextResponse } from 'next/server'
import { queryOne, db } from '@/lib/database'
import { z } from 'zod'

const updateOfficeSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  location: z.string().min(1).max(255).optional(),
})

// GET /api/offices/[id] - Get a specific office
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const office = await queryOne(
      'SELECT id, name, location, created_at, updated_at FROM offices WHERE id = $1',
      [id]
    )

    if (!office) {
      return NextResponse.json(
        { success: false, error: 'Office not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: office,
    })

  } catch (error) {
    console.error('Error fetching office:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch office' },
      { status: 500 }
    )
  }
}

// PUT /api/offices/[id] - Update a specific office
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    
    // Validate input
    const validatedData = updateOfficeSchema.parse(body)
    
    // Check if office exists
    const existingOffice = await queryOne('SELECT id FROM offices WHERE id = $1', [id])
    if (!existingOffice) {
      return NextResponse.json(
        { success: false, error: 'Office not found' },
        { status: 404 }
      )
    }

    // Build update query dynamically
    const updateFields = []
    const updateValues = []
    let paramIndex = 1

    if (validatedData.name !== undefined) {
      updateFields.push(`name = $${paramIndex}`)
      updateValues.push(validatedData.name)
      paramIndex++
    }

    if (validatedData.location !== undefined) {
      updateFields.push(`location = $${paramIndex}`)
      updateValues.push(validatedData.location)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    updateValues.push(id) // Add id as the last parameter

    const updateQuery = `
      UPDATE offices 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramIndex}
      RETURNING id, name, location, created_at, updated_at
    `

    const result = await db.query(updateQuery, updateValues)
    const updatedOffice = result.rows[0]

    return NextResponse.json({
      success: true,
      data: updatedOffice,
    })

  } catch (error) {
    console.error('Error updating office:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update office' },
      { status: 500 }
    )
  }
}

// DELETE /api/offices/[id] - Delete a specific office
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if office exists
    const existingOffice = await queryOne('SELECT id FROM offices WHERE id = $1', [id])
    if (!existingOffice) {
      return NextResponse.json(
        { success: false, error: 'Office not found' },
        { status: 404 }
      )
    }

    // Delete office (this will cascade to related records)
    await db.query('DELETE FROM offices WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'Office deleted successfully',
    })

  } catch (error) {
    console.error('Error deleting office:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete office' },
      { status: 500 }
    )
  }
}
