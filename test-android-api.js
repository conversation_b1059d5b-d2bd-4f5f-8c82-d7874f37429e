// Test script for Android emulator API access
const ANDROID_BASE_URL = 'http://********:3000/api';

async function testAndroidAPI() {
  console.log('Testing SRSR Backend API from Android Emulator perspective...\n');
  console.log('Base URL:', ANDROID_BASE_URL);
  console.log('');

  try {
    // Test root endpoint
    console.log('1. Testing root endpoint...');
    const rootResponse = await fetch(`${ANDROID_BASE_URL}`);
    const rootData = await rootResponse.json();
    console.log('✅ Root endpoint accessible from Android');
    console.log('API Name:', rootData.name);
    console.log('Status:', rootData.status);
    console.log('');

    // Test login with admin credentials
    console.log('2. Testing admin login...');
    const loginResponse = await fetch(`${ANDROID_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });
    
    const loginData = await loginResponse.json();
    let sessionCookie = '';
    
    if (loginData.success) {
      console.log('✅ Admin login successful');
      console.log('User:', loginData.user.username);
      console.log('Role:', loginData.user.role);
      
      // Extract session cookie for subsequent requests
      const setCookieHeader = loginResponse.headers.get('set-cookie');
      if (setCookieHeader) {
        sessionCookie = setCookieHeader.split(';')[0];
        console.log('Session cookie obtained');
      }
    } else {
      console.log('❌ Login failed:', loginData.error);
    }
    console.log('');

    // Test authenticated endpoint (sites)
    console.log('3. Testing authenticated sites endpoint...');
    const sitesResponse = await fetch(`${ANDROID_BASE_URL}/sites`, {
      headers: {
        'Cookie': sessionCookie
      }
    });
    const sitesData = await sitesResponse.json();
    if (sitesData.success) {
      console.log('✅ Sites endpoint accessible with authentication');
      console.log('Number of sites:', sitesData.data.length);
    } else {
      console.log('⚠️ Sites endpoint response:', sitesData.error || 'Unknown error');
    }
    console.log('');

    // Test creating a new site
    console.log('4. Testing site creation...');
    const createSiteResponse = await fetch(`${ANDROID_BASE_URL}/sites`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookie
      },
      body: JSON.stringify({
        name: 'Test Site from Android',
        location: 'Android Emulator Test Location'
      })
    });
    
    const createSiteData = await createSiteResponse.json();
    if (createSiteData.success) {
      console.log('✅ Site creation successful');
      console.log('Created site ID:', createSiteData.data.id);
    } else {
      console.log('⚠️ Site creation response:', createSiteData.error || 'Unknown error');
    }
    console.log('');

    // Test maintenance issues
    console.log('5. Testing maintenance issues endpoint...');
    const maintenanceResponse = await fetch(`${ANDROID_BASE_URL}/maintenance-issues?limit=5`, {
      headers: {
        'Cookie': sessionCookie
      }
    });
    const maintenanceData = await maintenanceResponse.json();
    if (maintenanceData.success) {
      console.log('✅ Maintenance issues endpoint working');
      console.log('Number of issues:', maintenanceData.data.length);
    } else {
      console.log('⚠️ Maintenance issues response:', maintenanceData.error || 'Unknown error');
    }
    console.log('');

    console.log('🎉 Android API testing completed!');
    console.log('');
    console.log('📱 For Android Development:');
    console.log('Base URL to use in your Android app:', ANDROID_BASE_URL);
    console.log('');
    console.log('📋 Sample Android HTTP Request:');
    console.log(`
// Using OkHttp or Retrofit in Android
String baseUrl = "${ANDROID_BASE_URL}";

// Login request
RequestBody loginBody = RequestBody.create(
    MediaType.parse("application/json"),
    "{\\"username\\":\\"admin\\",\\"password\\":\\"admin123\\"}"
);

Request loginRequest = new Request.Builder()
    .url(baseUrl + "/auth/login")
    .post(loginBody)
    .addHeader("Content-Type", "application/json")
    .build();
    `);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Make sure the API server is running: npm run dev');
    console.log('2. Check if the server is accessible from all interfaces (0.0.0.0)');
    console.log('3. Verify Android emulator can reach host machine');
    console.log('4. Try using your computer\'s IP address instead of ********');
    console.log('5. Check Windows Firewall settings if on Windows');
  }
}

// Run the test
testAndroidAPI();
