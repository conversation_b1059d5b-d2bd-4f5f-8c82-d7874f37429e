"use client"

import { useState } from "react"
import { CalendarIcon, Download, Search } from "lucide-react"
import { format } from "date-fns"

import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import { getAttendanceRecords } from "@/app/actions/attendance"

type AttendanceReportsProps = {
  siteId: string
  siteName: string
}

export function AttendanceReports({ siteId, siteName }: AttendanceReportsProps) {
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [isLoading, setIsLoading] = useState(false)
  const [records, setRecords] = useState<any[]>([])
  const { toast } = useToast()

  const fetchRecords = async () => {
    setIsLoading(true)

    try {
      const formattedStartDate = startDate ? format(startDate, "yyyy-MM-dd") : undefined
      const formattedEndDate = endDate ? format(endDate, "yyyy-MM-dd") : undefined

      const result = await getAttendanceRecords(siteId, formattedStartDate, formattedEndDate)

      if (result.success) {
        setRecords(result.data || [])
      } else {
        throw new Error(result.error || "Failed to fetch attendance records")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: (error as Error).message,
        variant: "destructive",
      })
      setRecords([])
    } finally {
      setIsLoading(false)
    }
  }

  const downloadCSV = () => {
    if (records.length === 0) {
      toast({
        title: "No data",
        description: "There are no records to download.",
      })
      return
    }

    // Create CSV content
    const headers = ["Site", "Worker Name", "Role", "Date", "Status", "Hours Worked", "Notes"]
    const csvRows = [headers]

    records.forEach((record) => {
      csvRows.push([
        siteName,
        record.worker_name,
        record.worker_role,
        record.date,
        record.status,
        record.hours_worked.toString(),
        record.notes || "",
      ])
    })

    const csvContent = csvRows.map((row) => row.join(",")).join("\n")

    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")

    link.setAttribute("href", url)
    link.setAttribute("download", `${siteName}_attendance_${format(new Date(), "yyyy-MM-dd")}.csv`)
    link.style.visibility = "hidden"

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
        <div className="grid gap-2">
          <div className="text-sm font-medium">Start Date</div>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn("w-full justify-start text-left font-normal", !startDate && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <div className="grid gap-2">
          <div className="text-sm font-medium">End Date</div>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn("w-full justify-start text-left font-normal", !endDate && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={endDate} onSelect={setEndDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <Button onClick={fetchRecords} disabled={isLoading}>
          {isLoading ? (
            "Loading..."
          ) : (
            <>
              <Search className="mr-2 h-4 w-4" />
              Search Records
            </>
          )}
        </Button>
      </div>

      {records.length > 0 ? (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">{records.length} Attendance Records</h3>
            <Button variant="outline" onClick={downloadCSV}>
              <Download className="mr-2 h-4 w-4" />
              Download CSV
            </Button>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Worker</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Hours</TableHead>
                  <TableHead>Notes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {records.map((record) => (
                  <TableRow key={`${record.worker_id}-${record.date}`}>
                    <TableCell className="font-medium">{record.worker_name}</TableCell>
                    <TableCell>{record.worker_role}</TableCell>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>
                      <span
                        className={cn(
                          "inline-block px-2 py-1 text-xs font-medium rounded-full",
                          record.status === "present" && "bg-green-100 text-green-800",
                          record.status === "absent" && "bg-red-100 text-red-800",
                          record.status === "late" && "bg-yellow-100 text-yellow-800",
                          record.status === "leave" && "bg-blue-100 text-blue-800",
                        )}
                      >
                        {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                      </span>
                    </TableCell>
                    <TableCell>{record.hours_worked}</TableCell>
                    <TableCell className="max-w-xs truncate">{record.notes}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          {isLoading ? "Loading records..." : "No attendance records found. Select a date range and search."}
        </div>
      )}
    </div>
  )
}
