@echo off
echo 🚀 SRSR Backend API Quick Start
echo ================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker first.
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Start PostgreSQL with Docker
echo 🐘 Starting PostgreSQL database...
docker-compose up -d postgres

REM Wait for PostgreSQL to be ready
echo ⏳ Waiting for PostgreSQL to be ready...
timeout /t 10 /nobreak >nul

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
)

REM Copy environment file if it doesn't exist
if not exist ".env" (
    echo 📝 Creating environment file...
    copy .env.example .env
)

REM Run database migration
echo 🗄️ Running database migration...
npm run db:migrate

REM Seed the database
echo 🌱 Seeding database with initial data...
npm run db:seed

echo.
echo 🎉 Setup complete!
echo.
echo To start the API server:
echo   npm run dev
echo.
echo To test the API:
echo   node test-api.js
echo.
echo Database admin interface:
echo   http://localhost:8080 (adminer)
echo   Server: postgres
echo   Username: postgres
echo   Password: password
echo   Database: srsr_backend
echo.
echo API documentation:
echo   http://localhost:3000/api
echo.
pause
