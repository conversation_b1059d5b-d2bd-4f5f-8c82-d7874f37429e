import { NextRequest, NextResponse } from 'next/server'
import { queryOne, db } from '@/lib/database'
import { z } from 'zod'

const updateOttServiceSchema = z.object({
  service_name: z.string().min(1).max(255).optional(),
  provider: z.string().min(1).max(255).optional(),
  subscription_type: z.string().min(1).max(100).optional(),
  monthly_cost: z.number().min(0).optional(),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  expiry_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  status: z.string().max(50).optional(),
  auto_renewal: z.boolean().optional(),
  login_credentials: z.string().optional(),
  notes: z.string().optional(),
})

// GET /api/ott-services/[id] - Get a specific OTT service
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const service = await queryOne(
      'SELECT * FROM ott_services WHERE id = $1',
      [id]
    )

    if (!service) {
      return NextResponse.json(
        { success: false, error: 'OTT service not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: service,
    })

  } catch (error) {
    console.error('Error fetching OTT service:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch OTT service' },
      { status: 500 }
    )
  }
}

// PUT /api/ott-services/[id] - Update a specific OTT service
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    // Validate input
    const validatedData = updateOttServiceSchema.parse(body)

    // Check if service exists
    const existingService = await queryOne('SELECT id FROM ott_services WHERE id = $1', [id])
    if (!existingService) {
      return NextResponse.json(
        { success: false, error: 'OTT service not found' },
        { status: 404 }
      )
    }

    // Build update query dynamically
    const updateFields = []
    const updateValues = []
    let paramIndex = 1

    Object.entries(validatedData).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = $${paramIndex}`)
        updateValues.push(value)
        paramIndex++
      }
    })

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    updateValues.push(id) // Add id as the last parameter

    const updateQuery = `
      UPDATE ott_services
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramIndex}
      RETURNING *
    `

    const result = await db.query(updateQuery, updateValues)
    const updatedService = result.rows[0]

    return NextResponse.json({
      success: true,
      data: updatedService,
    })

  } catch (error) {
    console.error('Error updating OTT service:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update OTT service' },
      { status: 500 }
    )
  }
}

// DELETE /api/ott-services/[id] - Delete a specific OTT service
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if service exists
    const existingService = await queryOne('SELECT id FROM ott_services WHERE id = $1', [id])
    if (!existingService) {
      return NextResponse.json(
        { success: false, error: 'OTT service not found' },
        { status: 404 }
      )
    }

    // Delete service
    await db.query('DELETE FROM ott_services WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'OTT service deleted successfully',
    })

  } catch (error) {
    console.error('Error deleting OTT service:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete OTT service' },
      { status: 500 }
    )
  }
}
