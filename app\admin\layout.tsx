import type React from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { getCurrentUser } from "@/lib/auth"
import { redirect } from "next/navigation"

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Get the current user
  const user = await getCurrentUser()

  // If no user, redirect to login
  if (!user) {
    redirect("/login")
  }

  // Check if user is admin1 or has admin role
  if (user.username !== "admin1" && user.role !== "admin") {
    // Also check user_roles table as a fallback
    const { createServerClient } = await import("@/lib/supabase")
    const supabase = createServerClient()

    const { data: userRoles, error } = await supabase.from("user_roles").select("roles(name)").eq("user_id", user.id)

    const isAdmin = userRoles?.some((ur) => ur.roles?.name === "admin")

    if (!isAdmin) {
      redirect("/unauthorized")
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="border-b bg-white">
        <div className="container mx-auto flex items-center justify-between p-4">
          <h1 className="text-xl font-bold">SRSR Admin</h1>
          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="outline">Back to Dashboard</Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="container mx-auto grid grid-cols-1 gap-6 p-4 md:grid-cols-[250px_1fr]">
        <div className="space-y-2">
          <Link href="/admin/users">
            <Button variant="ghost" className="w-full justify-start">
              User Management
            </Button>
          </Link>
          <Link href="/admin/roles">
            <Button variant="ghost" className="w-full justify-start">
              Role Management
            </Button>
          </Link>
          <Link href="/admin/permissions">
            <Button variant="ghost" className="w-full justify-start">
              Permission Management
            </Button>
          </Link>
        </div>

        <div>{children}</div>
      </div>
    </div>
  )
}
