"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Home,
  Building2,
  Zap,
  Droplets,
  Shield,
  Wifi,
  Monitor,
  Wrench,
  Users,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ChevronDown,
  ChevronUp,
} from "lucide-react"
import {
  type PropertyStatus,
  type FunctionalAreaStatus,
  getDashboardStatuses,
  getPropertyStatusSummary,
} from "@/app/actions/dashboard-status"
import Image from "next/image"

const functionalAreaIcons = {
  electricity: Zap,
  water: Droplets,
  security: Shield,
  internet: Wifi,
  ott: Monitor,
  maintenance: Wrench,
  attendance: Users,
}

const statusColors = {
  green: "bg-green-500",
  orange: "bg-orange-500",
  red: "bg-red-500",
}

const statusBadgeColors = {
  green: "bg-green-100 text-green-800 border-green-200",
  orange: "bg-orange-100 text-orange-800 border-orange-200",
  red: "bg-red-100 text-red-800 border-red-200",
}

export default function CurrentStatuses() {
  const [properties, setProperties] = useState<PropertyStatus[]>([])
  const [summary, setSummary] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [selectedType, setSelectedType] = useState<"all" | "home" | "office">("all")
  const [selectedStatus, setSelectedStatus] = useState<"all" | "green" | "orange" | "red">("all")
  const [expandedProperties, setExpandedProperties] = useState<Set<string>>(new Set())

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    try {
      const [propertiesData, summaryData] = await Promise.all([getDashboardStatuses(), getPropertyStatusSummary()])
      setProperties(propertiesData)
      setSummary(summaryData)
    } catch (error) {
      console.error("Error loading dashboard data:", error)
    } finally {
      setLoading(false)
    }
  }

  const filteredProperties = properties.filter((property) => {
    const typeMatch = selectedType === "all" || property.type === selectedType
    const statusMatch = selectedStatus === "all" || property.overallStatus === selectedStatus
    return typeMatch && statusMatch
  })

  const togglePropertyExpansion = (propertyId: string) => {
    const newExpanded = new Set(expandedProperties)
    if (newExpanded.has(propertyId)) {
      newExpanded.delete(propertyId)
    } else {
      newExpanded.add(propertyId)
    }
    setExpandedProperties(newExpanded)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 p-4">
        <div className="container mx-auto">
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin text-slate-400" />
            <span className="ml-2 text-slate-600">Loading property statuses...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 p-4">
      <div className="container mx-auto">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Current Statuses</h1>
            <p className="text-slate-600">Real-time overview of all property systems</p>
          </div>
          <Button onClick={loadData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-600">Total Properties</p>
                    <p className="text-2xl font-bold">{summary.totalProperties}</p>
                  </div>
                  <Building2 className="h-8 w-8 text-slate-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-600">Healthy</p>
                    <p className="text-2xl font-bold text-green-600">{summary.healthyProperties}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-600">Warning</p>
                    <p className="text-2xl font-bold text-orange-600">{summary.warningProperties}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-600">Critical</p>
                    <p className="text-2xl font-bold text-red-600">{summary.criticalProperties}</p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6">
          <Tabs value={selectedType} onValueChange={(value) => setSelectedType(value as any)}>
            <TabsList>
              <TabsTrigger value="all">All Properties</TabsTrigger>
              <TabsTrigger value="home">Home Properties</TabsTrigger>
              <TabsTrigger value="office">Office Properties</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="mt-4 flex gap-2">
            <Button
              variant={selectedStatus === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedStatus("all")}
            >
              All Status
            </Button>
            <Button
              variant={selectedStatus === "green" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedStatus("green")}
              className="border-green-200 text-green-700 hover:bg-green-50"
            >
              Healthy
            </Button>
            <Button
              variant={selectedStatus === "orange" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedStatus("orange")}
              className="border-orange-200 text-orange-700 hover:bg-orange-50"
            >
              Warning
            </Button>
            <Button
              variant={selectedStatus === "red" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedStatus("red")}
              className="border-red-200 text-red-700 hover:bg-red-50"
            >
              Critical
            </Button>
          </div>
        </div>

        {/* Property Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredProperties.map((property) => (
            <PropertyStatusCard
              key={property.id}
              property={property}
              isExpanded={expandedProperties.has(property.id)}
              onToggleExpansion={() => togglePropertyExpansion(property.id)}
            />
          ))}
        </div>

        {filteredProperties.length === 0 && (
          <div className="text-center py-12">
            <Building2 className="h-12 w-12 text-slate-300 mx-auto mb-4" />
            <p className="text-slate-500">No properties match the selected filters</p>
          </div>
        )}
      </div>
    </div>
  )
}

function PropertyStatusCard({
  property,
  isExpanded,
  onToggleExpansion,
}: {
  property: PropertyStatus
  isExpanded: boolean
  onToggleExpansion: () => void
}) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Image
                src={property.image || "/placeholder.svg"}
                alt={property.name}
                width={60}
                height={60}
                className="rounded-lg object-cover"
              />
              <div
                className={`absolute -top-1 -right-1 w-4 h-4 rounded-full ${statusColors[property.overallStatus]}`}
              />
            </div>
            <div>
              <CardTitle className="text-lg">{property.name}</CardTitle>
              <CardDescription className="flex items-center gap-2">
                {property.type === "home" ? <Home className="h-4 w-4" /> : <Building2 className="h-4 w-4" />}
                {property.type === "home" ? "Residential" : "Office"}
              </CardDescription>
            </div>
          </div>
          <Badge className={statusBadgeColors[property.overallStatus]}>{property.overallStatus.toUpperCase()}</Badge>
        </div>
      </CardHeader>

      <CardContent>
        {/* Functional Areas Overview */}
        <div className="grid grid-cols-4 gap-2 mb-4">
          {Object.entries(property.functionalAreas).map(([area, status]) => {
            const Icon = functionalAreaIcons[area as keyof typeof functionalAreaIcons]
            return (
              <div key={area} className="flex flex-col items-center p-2 rounded-lg bg-slate-50">
                <Icon className="h-5 w-5 text-slate-600 mb-1" />
                <div className={`w-3 h-3 rounded-full ${statusColors[status.status]}`} />
              </div>
            )
          })}
        </div>

        {/* Expand/Collapse Button */}
        <Button variant="ghost" size="sm" onClick={onToggleExpansion} className="w-full">
          {isExpanded ? (
            <>
              <ChevronUp className="h-4 w-4 mr-2" />
              Hide Details
            </>
          ) : (
            <>
              <ChevronDown className="h-4 w-4 mr-2" />
              Show Details
            </>
          )}
        </Button>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="mt-4 space-y-4">
            {Object.entries(property.functionalAreas).map(([area, areaStatus]) => (
              <FunctionalAreaDetails key={area} area={area} status={areaStatus} />
            ))}
          </div>
        )}

        <div className="mt-4 text-xs text-slate-500">
          Last updated: {new Date(property.lastUpdated).toLocaleString()}
        </div>
      </CardContent>
    </Card>
  )
}

function FunctionalAreaDetails({ area, status }: { area: string; status: FunctionalAreaStatus }) {
  const Icon = functionalAreaIcons[area as keyof typeof functionalAreaIcons]

  return (
    <div className="border rounded-lg p-3">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4" />
          <span className="font-medium capitalize">{area}</span>
        </div>
        <Badge className={statusBadgeColors[status.status]} variant="outline">
          {status.status.toUpperCase()}
        </Badge>
      </div>

      <div className="space-y-1">
        {status.metrics.map((metric, index) => (
          <div key={index} className="flex items-center justify-between text-sm">
            <span className="text-slate-600 capitalize">{metric.name.replace("_", " ")}</span>
            <div className="flex items-center gap-2">
              <span>
                {typeof metric.value === "boolean" ? (metric.value ? "On" : "Off") : `${metric.value}${metric.unit}`}
              </span>
              <div className={`w-2 h-2 rounded-full ${statusColors[metric.status]}`} />
            </div>
          </div>
        ))}
      </div>

      {status.issueCount > 0 && (
        <div className="mt-2 text-xs text-orange-600">
          {status.issueCount} issue{status.issueCount > 1 ? "s" : ""} detected
        </div>
      )}
    </div>
  )
}
