import { NextRequest, NextResponse } from 'next/server'
import { queryOne, queryInsert } from '@/lib/database'
import { verifyPassword } from '@/lib/auth'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'

const loginSchema = z.object({
  username: z.string().min(1),
  password: z.string().min(1),
})

const SESSION_DURATION = 24 * 60 * 60 // 24 hours in seconds

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = loginSchema.parse(body)
    const { username, password } = validatedData

    // Find user by username
    const user = await queryOne(
      'SELECT id, username, password, full_name, email, role, is_active FROM users WHERE username = $1',
      [username]
    )

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Check if user is active
    if (!user.is_active) {
      return NextResponse.json(
        { success: false, error: 'Account is not active. Please contact administrator.' },
        { status: 401 }
      )
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.password)
    if (!isValidPassword) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Create session
    const token = uuidv4()
    const expiresAt = new Date()
    expiresAt.setSeconds(expiresAt.getSeconds() + SESSION_DURATION)

    await queryInsert(
      'INSERT INTO sessions (user_id, token, expires_at) VALUES ($1, $2, $3)',
      [user.id, token, expiresAt.toISOString()]
    )

    // Create response with session cookie
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        fullName: user.full_name,
        email: user.email,
        role: user.role,
      },
      token,
    })

    // Set session cookie
    response.cookies.set('session_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      expires: expiresAt,
      path: '/',
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Login failed' },
      { status: 500 }
    )
  }
}
