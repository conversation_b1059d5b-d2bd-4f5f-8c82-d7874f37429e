# SRSR Backend API

A Next.js-based REST API server for the SRSR property management system, converted from a full-stack application to use local PostgreSQL instead of Supabase.

## Features

- **Authentication**: User registration, login, logout with session management
- **Sites Management**: CRUD operations for sites and site members
- **Offices Management**: CRUD operations for offices and office members
- **Maintenance Issues**: Track and manage maintenance issues
- **Generator Fuel**: Monitor generator fuel levels and updates
- **OTT Services**: Manage OTT service subscriptions
- **Uptime Reports**: Track property uptime and downtime
- **Attendance**: Site and office attendance tracking
- **Role-based Access Control**: Admin, manager, user, and viewer roles

## Prerequisites

- Node.js 18+ 
- PostgreSQL 12+
- npm or pnpm

## Setup

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd srsr-backend
npm install
```

### 2. Database Setup

1. Create a PostgreSQL database:
```sql
CREATE DATABASE srsr_backend;
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Update `.env` with your database credentials:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=srsr_backend
DB_USER=postgres
DB_PASSWORD=your_password
```

4. Run database migration:
```bash
npm run db:migrate
```

5. Seed initial data:
```bash
npm run db:seed
```

### 3. Start the Server

```bash
# Development
npm run dev

# Production
npm run build
npm start
```

The API will be available at `http://localhost:3000`

## API Documentation

### Base URL
```
http://localhost:3000/api
```

### Authentication

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "john_doe",
  "password": "password123",
  "fullName": "John Doe"
}
```

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "john_doe",
  "password": "password123"
}
```

#### Logout
```http
POST /api/auth/logout
```

### Sites

#### Get All Sites
```http
GET /api/sites
```

#### Create Site
```http
POST /api/sites
Content-Type: application/json

{
  "name": "Site Name",
  "location": "Site Location"
}
```

#### Get Site by ID
```http
GET /api/sites/{id}
```

#### Update Site
```http
PUT /api/sites/{id}
Content-Type: application/json

{
  "name": "Updated Site Name",
  "location": "Updated Location"
}
```

#### Delete Site
```http
DELETE /api/sites/{id}
```

#### Get Site Members
```http
GET /api/sites/{id}/members
```

#### Add Site Member
```http
POST /api/sites/{id}/members
Content-Type: application/json

{
  "name": "Member Name",
  "mobile_number": "1234567890",
  "role": "Security Guard",
  "duty_time": "9 AM - 6 PM"
}
```

### Maintenance Issues

#### Get All Issues
```http
GET /api/maintenance-issues?property_id={uuid}&status=Open&limit=50
```

#### Create Issue
```http
POST /api/maintenance-issues
Content-Type: application/json

{
  "property_id": "uuid",
  "issue_type": "Electrical",
  "category": "Power Outage",
  "issue_date": "2024-01-15",
  "reported_by": "John Doe",
  "priority": "High"
}
```

## Database Schema

The application uses the following main tables:

- `users` - User accounts and authentication
- `sessions` - User sessions
- `roles` - User roles (admin, manager, user, viewer)
- `permissions` - Role-based permissions
- `sites` - Property sites
- `site_members` - Site staff members
- `offices` - Office locations
- `office_members` - Office staff members
- `site_attendance` - Site attendance records
- `office_attendance` - Office attendance records
- `maintenance_issues` - Maintenance issue tracking
- `escalation_matrix` - Issue escalation tracking
- `generator_fuel_updates` - Generator fuel monitoring
- `ott_services` - OTT service management
- `uptime_reports` - Property uptime tracking
- `threshold_config` - Alert thresholds
- `function_process_matrix` - Process documentation
- `diesel_additions` - Diesel fuel additions

## Default Admin Account

After running the seed script, you can login with:
- Username: `admin`
- Password: `admin123`

## Development

### Adding New API Routes

1. Create route files in `app/api/` directory
2. Follow the existing pattern for error handling and validation
3. Use Zod for input validation
4. Use the database helper functions from `lib/database.ts`

### Database Queries

Use the helper functions from `lib/database.ts`:

```typescript
import { queryOne, queryMany, queryInsert, db } from '@/lib/database'

// Single record
const user = await queryOne('SELECT * FROM users WHERE id = $1', [userId])

// Multiple records
const sites = await queryMany('SELECT * FROM sites ORDER BY name')

// Insert with return
const newSite = await queryInsert(
  'INSERT INTO sites (name, location) VALUES ($1, $2) RETURNING *',
  [name, location]
)

// Complex queries
const result = await db.query('SELECT * FROM complex_view WHERE condition = $1', [value])
```

## Production Deployment

1. Set `NODE_ENV=production` in environment
2. Configure production database credentials
3. Run `npm run build`
4. Start with `npm start`
5. Consider using PM2 or similar process manager
6. Set up reverse proxy (nginx) for SSL and load balancing

## License

Private - SRSR Property Management System
