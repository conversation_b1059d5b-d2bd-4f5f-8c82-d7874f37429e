import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createMaintenanceIssueSchema = z.object({
  property_id: z.string().uuid(),
  issue_type: z.string().min(1).max(100),
  category: z.string().min(1).max(100),
  issue_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  status: z.string().max(50).default('Open'),
  resolution_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  remarks: z.string().optional(),
  reported_by: z.string().min(1).max(255),
  assigned_to: z.string().max(255).optional(),
  priority: z.string().max(20).default('Medium'),
})

// GET /api/maintenance-issues - Get all maintenance issues
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propertyId = searchParams.get('property_id')
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')

    let query = `
      SELECT id, property_id, issue_type, category, issue_date, status, 
             resolution_date, remarks, reported_by, assigned_to, priority,
             created_at, updated_at 
      FROM maintenance_issues
    `
    const params: any[] = []
    const conditions: string[] = []

    if (propertyId) {
      conditions.push(`property_id = $${params.length + 1}`)
      params.push(propertyId)
    }

    if (status) {
      conditions.push(`status = $${params.length + 1}`)
      params.push(status)
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    query += ' ORDER BY issue_date DESC, created_at DESC'

    if (limit) {
      query += ` LIMIT $${params.length + 1}`
      params.push(parseInt(limit))
    }

    const issues = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: issues,
    })

  } catch (error) {
    console.error('Error fetching maintenance issues:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch maintenance issues' },
      { status: 500 }
    )
  }
}

// POST /api/maintenance-issues - Create a new maintenance issue
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createMaintenanceIssueSchema.parse(body)
    const {
      property_id,
      issue_type,
      category,
      issue_date,
      status,
      resolution_date,
      remarks,
      reported_by,
      assigned_to,
      priority,
    } = validatedData

    // Insert new maintenance issue
    const newIssue = await queryInsert(
      `INSERT INTO maintenance_issues 
       (property_id, issue_type, category, issue_date, status, resolution_date, 
        remarks, reported_by, assigned_to, priority) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) 
       RETURNING id, property_id, issue_type, category, issue_date, status, 
                 resolution_date, remarks, reported_by, assigned_to, priority,
                 created_at, updated_at`,
      [
        property_id,
        issue_type,
        category,
        issue_date,
        status,
        resolution_date,
        remarks,
        reported_by,
        assigned_to,
        priority,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newIssue,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating maintenance issue:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create maintenance issue' },
      { status: 500 }
    )
  }
}
