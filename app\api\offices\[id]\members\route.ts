import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert, queryOne } from '@/lib/database'
import { z } from 'zod'

const createOfficeMemberSchema = z.object({
  name: z.string().min(1).max(255),
  mobile_number: z.string().max(20).optional(),
  role: z.string().max(100).optional(),
  duty_time: z.string().max(100).optional(),
  remarks: z.string().optional(),
})

// GET /api/offices/[id]/members - Get all members for an office
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: officeId } = params

    // Check if office exists
    const office = await queryOne('SELECT id, location FROM offices WHERE id = $1', [officeId])
    if (!office) {
      return NextResponse.json(
        { success: false, error: 'Office not found' },
        { status: 404 }
      )
    }

    const members = await queryMany(
      `SELECT id, name, mobile_number, office_location, role, duty_time, remarks, created_at, updated_at 
       FROM office_members 
       WHERE office_location = $1 
       ORDER BY name`,
      [office.location]
    )

    return NextResponse.json({
      success: true,
      data: members,
    })

  } catch (error) {
    console.error('Error fetching office members:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch office members' },
      { status: 500 }
    )
  }
}

// POST /api/offices/[id]/members - Create a new member for an office
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: officeId } = params
    const body = await request.json()
    
    // Validate input
    const validatedData = createOfficeMemberSchema.parse(body)
    const { name, mobile_number, role, duty_time, remarks } = validatedData

    // Check if office exists and get location
    const office = await queryOne('SELECT id, location FROM offices WHERE id = $1', [officeId])
    if (!office) {
      return NextResponse.json(
        { success: false, error: 'Office not found' },
        { status: 404 }
      )
    }

    // Insert new member
    const newMember = await queryInsert(
      `INSERT INTO office_members (name, mobile_number, office_location, role, duty_time, remarks) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING id, name, mobile_number, office_location, role, duty_time, remarks, created_at, updated_at`,
      [name, mobile_number, office.location, role, duty_time, remarks]
    )

    return NextResponse.json({
      success: true,
      data: newMember,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating office member:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create office member' },
      { status: 500 }
    )
  }
}
