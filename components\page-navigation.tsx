"use client"

import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Home } from "lucide-react"

interface PageNavigationProps {
  className?: string
}

export function PageNavigation({ className }: PageNavigationProps) {
  const router = useRouter()

  const goBack = () => {
    router.back()
  }

  const goHome = () => {
    router.push("/dashboard")
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Button variant="outline" size="sm" onClick={goBack}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </Button>
      <Button variant="outline" size="sm" onClick={goHome}>
        <Home className="mr-2 h-4 w-4" />
        Home
      </Button>
    </div>
  )
}
