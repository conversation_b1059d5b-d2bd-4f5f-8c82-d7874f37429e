import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createOfficeAttendanceSchema = z.object({
  member_id: z.string().uuid(),
  member_name: z.string().min(1).max(255).optional(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  status: z.enum(['Present', 'Absent', 'Half Day', 'Leave']),
  check_in: z.string().regex(/^\d{2}:\d{2}(:\d{2})?$/).optional(),
  check_out: z.string().regex(/^\d{2}:\d{2}(:\d{2})?$/).optional(),
  hours_worked: z.number().min(0).max(24).optional(),
  remarks: z.string().optional(),
  office_location: z.string().max(255).optional(),
})

// GET /api/attendance/office - Get office attendance records
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const memberId = searchParams.get('member_id')
    const date = searchParams.get('date')
    const officeLocation = searchParams.get('office_location')
    const limit = searchParams.get('limit')

    let query = `
      SELECT id, member_id, member_name, date, status, check_in, check_out,
             hours_worked, remarks, office_location, created_at 
      FROM office_attendance
    `
    const params: any[] = []
    const conditions: string[] = []

    if (memberId) {
      conditions.push(`member_id = $${params.length + 1}`)
      params.push(memberId)
    }

    if (date) {
      conditions.push(`date = $${params.length + 1}`)
      params.push(date)
    }

    if (officeLocation) {
      conditions.push(`office_location = $${params.length + 1}`)
      params.push(officeLocation)
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    query += ' ORDER BY date DESC, member_name'

    if (limit) {
      query += ` LIMIT $${params.length + 1}`
      params.push(parseInt(limit))
    }

    const records = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: records,
    })

  } catch (error) {
    console.error('Error fetching office attendance:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch office attendance' },
      { status: 500 }
    )
  }
}

// POST /api/attendance/office - Create a new office attendance record
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createOfficeAttendanceSchema.parse(body)
    const {
      member_id,
      member_name,
      date,
      status,
      check_in,
      check_out,
      hours_worked,
      remarks,
      office_location,
    } = validatedData

    // Insert new attendance record
    const newRecord = await queryInsert(
      `INSERT INTO office_attendance 
       (member_id, member_name, date, status, check_in, check_out, 
        hours_worked, remarks, office_location) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
       RETURNING id, member_id, member_name, date, status, check_in, check_out,
                 hours_worked, remarks, office_location, created_at`,
      [
        member_id,
        member_name,
        date,
        status,
        check_in,
        check_out,
        hours_worked,
        remarks,
        office_location,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newRecord,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating office attendance:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create office attendance record' },
      { status: 500 }
    )
  }
}
