import { Suspense } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertTriangle, CheckCircle, AlertCircle } from "lucide-react"
import { PropertyStatusList } from "@/components/property-status-list"
import { getDashboardStatuses, getPropertyStatusSummary } from "@/app/actions/dashboard-status"

// Loading component for summary cards
function SummaryLoading() {
  return (
    <div className="grid gap-4 md:grid-cols-4 mb-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="pb-2">
            <Skeleton className="h-4 w-24" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Summary cards component
async function SummaryCards() {
  try {
    const summary = await getPropertyStatusSummary()

    return (
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalProperties}</div>
          </CardContent>
        </Card>
        <Card className="bg-green-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Healthy</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <div className="text-2xl font-bold text-green-700">{summary.healthyProperties}</div>
          </CardContent>
        </Card>
        <Card className="bg-orange-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Warning</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            <div className="text-2xl font-bold text-orange-700">{summary.warningProperties}</div>
          </CardContent>
        </Card>
        <Card className="bg-red-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-red-700">Critical</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <div className="text-2xl font-bold text-red-700">{summary.criticalProperties}</div>
          </CardContent>
        </Card>
      </div>
    )
  } catch (error) {
    console.error("Error loading summary:", error)
    return (
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card className="col-span-4">
          <CardContent className="p-4">
            <div className="text-center text-red-600">Error loading summary data. Please try refreshing the page.</div>
          </CardContent>
        </Card>
      </div>
    )
  }
}

// Property list component
async function PropertyList() {
  try {
    const statuses = await getDashboardStatuses()
    return <PropertyStatusList statuses={statuses} type="all" />
  } catch (error) {
    console.error("Error loading property statuses:", error)
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error Loading Properties</h3>
          <p className="text-muted-foreground mb-4">
            There was an error loading property status data. This might be due to database connectivity issues.
          </p>
          <p className="text-sm text-red-600">Error: {error instanceof Error ? error.message : "Unknown error"}</p>
        </CardContent>
      </Card>
    )
  }
}

export default function StatusPage() {
  return (
    <div className="container mx-auto p-4">
      <div className="my-6">
        <h1 className="text-3xl font-bold">Current Statuses</h1>
        <p className="text-muted-foreground">Real-time status of all properties and functional areas</p>
      </div>

      <Suspense fallback={<SummaryLoading />}>
        <SummaryCards />
      </Suspense>

      <div className="mb-4">
        <h2 className="text-xl font-semibold">Property Overview</h2>
        <p className="text-sm text-muted-foreground">Click on any property to view detailed status information</p>
      </div>

      <Suspense
        fallback={
          <div className="grid gap-6 md:grid-cols-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-16 w-16 rounded-lg" />
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        }
      >
        <PropertyList />
      </Suspense>
    </div>
  )
}
