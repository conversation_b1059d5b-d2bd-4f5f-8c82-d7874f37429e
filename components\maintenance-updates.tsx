"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Pencil, Save, Trash2, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { updateGeneratorFuelEntry, deleteGeneratorFuelEntry } from "@/app/actions/generator-fuel"
import type { GeneratorFuelUpdate } from "@/app/actions/generator-fuel"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface MaintenanceUpdatesProps {
  updates: GeneratorFuelUpdate[]
  onRefresh: () => void
}

export function MaintenanceUpdates({ updates, onRefresh }: MaintenanceUpdatesProps) {
  const { toast } = useToast()
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editValues, setEditValues] = useState<Partial<GeneratorFuelUpdate>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const startEditing = (update: GeneratorFuelUpdate) => {
    setEditingId(update.id)
    setEditValues({
      date: update.date,
      starting_reading: update.starting_reading,
      ending_reading: update.ending_reading,
      fuel_in_generator_percentage: update.fuel_in_generator_percentage,
      fuel_in_tank_liters: update.fuel_in_tank_liters,
    })
  }

  const cancelEditing = () => {
    setEditingId(null)
    setEditValues({})
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Handle numeric values
    if (
      name === "starting_reading" ||
      name === "ending_reading" ||
      name === "fuel_in_generator_percentage" ||
      name === "fuel_in_tank_liters"
    ) {
      setEditValues({
        ...editValues,
        [name]: value === "" ? "" : Number.parseFloat(value),
      })
    } else {
      setEditValues({
        ...editValues,
        [name]: value,
      })
    }
  }

  const saveChanges = async () => {
    if (editingId === null) return

    setIsSubmitting(true)

    try {
      // Validate inputs
      if (
        editValues.starting_reading === undefined ||
        editValues.ending_reading === undefined ||
        editValues.fuel_in_generator_percentage === undefined ||
        editValues.fuel_in_tank_liters === undefined ||
        editValues.date === undefined
      ) {
        toast({
          title: "Validation Error",
          description: "All fields are required",
          variant: "destructive",
        })
        return
      }

      const result = await updateGeneratorFuelEntry(editingId, {
        date: editValues.date,
        starting_reading: editValues.starting_reading,
        ending_reading: editValues.ending_reading,
        fuel_in_generator_percentage: editValues.fuel_in_generator_percentage,
        fuel_in_tank_liters: editValues.fuel_in_tank_liters,
      })

      if (result.success) {
        toast({
          title: "Success",
          description: "Generator fuel entry has been updated.",
          variant: "default",
        })
        cancelEditing()
        onRefresh()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update entry",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating entry:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async () => {
    if (!deleteId) return

    setIsSubmitting(true)

    try {
      const result = await deleteGeneratorFuelEntry(deleteId)

      if (result.success) {
        toast({
          title: "Success",
          description: "Generator fuel entry has been deleted.",
          variant: "default",
        })
        onRefresh()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete entry",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting entry:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setShowDeleteDialog(false)
      setDeleteId(null)
    }
  }

  const confirmDelete = (id: number) => {
    setDeleteId(id)
    setShowDeleteDialog(true)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "-"
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Maintenance Updates</CardTitle>
          <CardDescription>History of generator fuel updates</CardDescription>
        </CardHeader>
        <CardContent>
          {updates.length === 0 ? (
            <div className="rounded-md bg-gray-50 p-4 text-center text-gray-500">No generator fuel updates found.</div>
          ) : (
            <div className="rounded-md border overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Starting Reading</TableHead>
                    <TableHead>Ending Reading</TableHead>
                    <TableHead>Fuel in Generator (%)</TableHead>
                    <TableHead>Fuel outside Generator (L)</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {updates.map((update) => (
                    <TableRow key={update.id}>
                      <TableCell>
                        {editingId === update.id ? (
                          <Input
                            type="date"
                            name="date"
                            value={editValues.date}
                            onChange={handleInputChange}
                            className="w-full"
                          />
                        ) : (
                          formatDate(update.date)
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === update.id ? (
                          <Input
                            type="number"
                            name="starting_reading"
                            value={editValues.starting_reading}
                            onChange={handleInputChange}
                            step="0.1"
                            min="0"
                            className="w-full"
                          />
                        ) : (
                          update.starting_reading
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === update.id ? (
                          <Input
                            type="number"
                            name="ending_reading"
                            value={editValues.ending_reading}
                            onChange={handleInputChange}
                            step="0.1"
                            min="0"
                            className="w-full"
                          />
                        ) : (
                          update.ending_reading
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === update.id ? (
                          <Input
                            type="number"
                            name="fuel_in_generator_percentage"
                            value={editValues.fuel_in_generator_percentage}
                            onChange={handleInputChange}
                            min="0"
                            max="100"
                            className="w-full"
                          />
                        ) : (
                          `${update.fuel_in_generator_percentage}%`
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === update.id ? (
                          <Input
                            type="number"
                            name="fuel_in_tank_liters"
                            value={editValues.fuel_in_tank_liters}
                            onChange={handleInputChange}
                            step="0.1"
                            min="0"
                            className="w-full"
                          />
                        ) : (
                          update.fuel_in_tank_liters
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          {editingId === update.id ? (
                            <>
                              <Button variant="ghost" size="icon" onClick={saveChanges} disabled={isSubmitting}>
                                <Save className="h-4 w-4 text-green-500" />
                                <span className="sr-only">Save</span>
                              </Button>
                              <Button variant="ghost" size="icon" onClick={cancelEditing} disabled={isSubmitting}>
                                <X className="h-4 w-4 text-red-500" />
                                <span className="sr-only">Cancel</span>
                              </Button>
                            </>
                          ) : (
                            <>
                              <Button variant="ghost" size="icon" onClick={() => startEditing(update)}>
                                <Pencil className="h-4 w-4 text-blue-500" />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button variant="ghost" size="icon" onClick={() => confirmDelete(update.id)}>
                                <Trash2 className="h-4 w-4 text-red-500" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the generator fuel entry.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} disabled={isSubmitting} className="bg-red-600 hover:bg-red-700">
              {isSubmitting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
