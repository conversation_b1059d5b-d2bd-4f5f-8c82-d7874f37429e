import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createSiteAttendanceSchema = z.object({
  site_id: z.string().uuid(),
  worker_id: z.string().uuid(),
  worker_name: z.string().min(1).max(255),
  worker_role: z.string().min(1).max(100),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  status: z.enum(['Present', 'Absent', 'Half Day', 'Leave']),
  hours_worked: z.number().min(0).max(24).default(0),
  notes: z.string().optional(),
})

// GET /api/attendance/site - Get site attendance records
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const siteId = searchParams.get('site_id')
    const date = searchParams.get('date')
    const workerId = searchParams.get('worker_id')
    const limit = searchParams.get('limit')

    let query = `
      SELECT id, site_id, worker_id, worker_name, worker_role, date, 
             status, hours_worked, notes, created_at 
      FROM site_attendance
    `
    const params: any[] = []
    const conditions: string[] = []

    if (siteId) {
      conditions.push(`site_id = $${params.length + 1}`)
      params.push(siteId)
    }

    if (date) {
      conditions.push(`date = $${params.length + 1}`)
      params.push(date)
    }

    if (workerId) {
      conditions.push(`worker_id = $${params.length + 1}`)
      params.push(workerId)
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    query += ' ORDER BY date DESC, worker_name'

    if (limit) {
      query += ` LIMIT $${params.length + 1}`
      params.push(parseInt(limit))
    }

    const records = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: records,
    })

  } catch (error) {
    console.error('Error fetching site attendance:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch site attendance' },
      { status: 500 }
    )
  }
}

// POST /api/attendance/site - Create a new site attendance record
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createSiteAttendanceSchema.parse(body)
    const {
      site_id,
      worker_id,
      worker_name,
      worker_role,
      date,
      status,
      hours_worked,
      notes,
    } = validatedData

    // Insert new attendance record
    const newRecord = await queryInsert(
      `INSERT INTO site_attendance 
       (site_id, worker_id, worker_name, worker_role, date, status, hours_worked, notes) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
       RETURNING id, site_id, worker_id, worker_name, worker_role, date, 
                 status, hours_worked, notes, created_at`,
      [
        site_id,
        worker_id,
        worker_name,
        worker_role,
        date,
        status,
        hours_worked,
        notes,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newRecord,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating site attendance:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create site attendance record' },
      { status: 500 }
    )
  }
}
