import { db, queryOne, queryInsert } from "@/lib/database"
import { cookies } from "next/headers"
import * as bcrypt from "bcryptjs"
import { v4 as uuidv4 } from "uuid"

// Types
export type User = {
  id: string
  username: string
  email: string
  full_name: string
  role: string
  is_active: boolean
}

export type Role = {
  id: number
  name: string
  description: string
}

export type Permission = {
  id: number
  url_pattern: string
  role_id: number
}

// Session duration in seconds (24 hours)
const SESSION_DURATION = 24 * 60 * 60

// Hash password
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10)
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// Create session
export async function createSession(userId: string): Promise<string> {
  const token = uuidv4()
  const expiresAt = new Date()
  expiresAt.setSeconds(expiresAt.getSeconds() + SESSION_DURATION)

  await queryInsert(
    'INSERT INTO sessions (user_id, token, expires_at) VALUES ($1, $2, $3)',
    [userId, token, expiresAt.toISOString()]
  )

  // Set cookie
  cookies().set("session_token", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    expires: expiresAt,
    path: "/",
  })

  return token
}

// Get current session
export async function getCurrentSession() {
  const token = cookies().get("session_token")?.value

  if (!token) {
    return null
  }

  const session = await queryOne(
    'SELECT * FROM sessions WHERE token = $1 AND expires_at > $2',
    [token, new Date().toISOString()]
  )

  if (!session) {
    cookies().delete("session_token")
    return null
  }

  return session
}

// Get current user
export async function getCurrentUser(): Promise<User | null> {
  const session = await getCurrentSession()
  if (!session) {
    return null
  }

  const user = await queryOne(
    'SELECT * FROM users WHERE id = $1 AND is_active = true',
    [session.user_id]
  )

  if (!user) {
    return null
  }

  return user as User
}

// Get user roles (simplified for basic role system)
export async function getUserRoles(userId: string): Promise<Role[]> {
  const user = await queryOne('SELECT role FROM users WHERE id = $1', [userId])

  if (!user || !user.role) {
    return []
  }

  const role = await queryOne('SELECT * FROM roles WHERE name = $1', [user.role])

  return role ? [role as Role] : []
}

// Check if user has permission to access URL
export async function hasPermission(userId: string, url: string): Promise<boolean> {
  // First check if user is admin
  const user = await queryOne('SELECT role FROM users WHERE id = $1', [userId])

  // Admin users have access to everything
  if (user && user.role === "admin") {
    return true
  }

  // Get user's role
  const role = await queryOne('SELECT id FROM roles WHERE name = $1', [user?.role])

  if (!role) {
    return false
  }

  // Get permissions for this role
  const permissions = await db.query(
    'SELECT * FROM permissions WHERE role_id = $1',
    [role.id]
  )

  if (!permissions.rows || permissions.rows.length === 0) {
    return false
  }

  // Check if any permission pattern matches the URL
  return permissions.rows.some((permission) => {
    const pattern = permission.url_pattern
      .replace(/\*/g, ".*") // Convert * to .* for regex
      .replace(/\//g, "\\/") // Escape / for regex

    const regex = new RegExp(`^${pattern}$`)
    return regex.test(url)
  })
}

// Logout
export async function logout() {
  const token = cookies().get("session_token")?.value

  if (token) {
    await db.query('DELETE FROM sessions WHERE token = $1', [token])
    cookies().delete("session_token")
  }
}

// Auth middleware for API routes
export async function requireAuth() {
  const user = await getCurrentUser()

  if (!user) {
    throw new Error('Authentication required')
  }

  return user
}

// Role middleware for API routes
export async function requireRole(roles: string[]) {
  const user = await requireAuth()

  // Admin users bypass role checks
  if (user.role === "admin") {
    return user
  }

  // Check if user's role property is in the required roles
  if (roles.includes(user.role)) {
    return user
  }

  // Fallback to checking user_roles table
  const userRoles = await getUserRoles(user.id)
  const hasRole = userRoles.some((role) => roles.includes(role.name))

  if (!hasRole) {
    throw new Error('Insufficient permissions')
  }

  return user
}

// URL permission middleware for API routes
export async function requirePermission(url: string) {
  const user = await requireAuth()

  // Admin users bypass permission checks
  if (user.role === "admin") {
    return user
  }

  const hasAccess = await hasPermission(user.id, url)

  if (!hasAccess) {
    throw new Error('Access denied')
  }

  return user
}
