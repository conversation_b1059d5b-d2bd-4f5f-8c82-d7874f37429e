version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: srsr_postgres
    environment:
      POSTGRES_DB: srsr_backend
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    restart: unless-stopped

  adminer:
    image: adminer
    container_name: srsr_adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
