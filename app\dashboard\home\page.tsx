"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Home, ArrowRight } from "lucide-react"
import Link from "next/link"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { PageNavigation } from "@/components/page-navigation"

export default function HomePropertiesPage() {
  const properties = [
    {
      id: "jublee-hills",
      name: "Jublee Hills Home",
      description: "Residential property in Jublee Hills",
    },
    {
      id: "gandipet-guest-house",
      name: "Gandipet Guest House",
      description: "Guest house in Gandipet",
    },
  ]

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/home">Home</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <h1 className="mb-8 text-3xl font-bold">Residential Properties</h1>

        <div className="grid gap-6 md:grid-cols-2">
          {properties.map((property) => (
            <Link key={property.id} href={`/dashboard/home/<USER>"block">
              <Card className="h-full transition-all hover:shadow-md">
                <CardHeader className="flex flex-row items-center gap-4">
                  <Home className="h-8 w-8 text-slate-700" />
                  <div>
                    <CardTitle>{property.name}</CardTitle>
                    <CardDescription>{property.description}</CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="flex justify-end">
                  <ArrowRight className="h-5 w-5 text-slate-400" />
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
