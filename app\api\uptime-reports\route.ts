import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createUptimeReportSchema = z.object({
  property_id: z.string().uuid(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  downtime_duration: z.string().max(50).optional(),
  uptime_percentage: z.string().max(10).optional(),
  event_time_ist: z.string().max(50).optional(),
  reason_for_disruption: z.string().optional(),
})

// GET /api/uptime-reports - Get all uptime reports
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propertyId = searchParams.get('property_id')
    const limit = searchParams.get('limit')

    let query = `
      SELECT id, property_id, date, downtime_duration, uptime_percentage,
             event_time_ist, reason_for_disruption, created_at, updated_at 
      FROM uptime_reports
    `
    const params: any[] = []

    if (propertyId) {
      query += ' WHERE property_id = $1'
      params.push(propertyId)
    }

    query += ' ORDER BY date DESC, created_at DESC'

    if (limit) {
      const limitIndex = params.length + 1
      query += ` LIMIT $${limitIndex}`
      params.push(parseInt(limit))
    }

    const reports = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: reports,
    })

  } catch (error) {
    console.error('Error fetching uptime reports:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch uptime reports' },
      { status: 500 }
    )
  }
}

// POST /api/uptime-reports - Create a new uptime report
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createUptimeReportSchema.parse(body)
    const {
      property_id,
      date,
      downtime_duration,
      uptime_percentage,
      event_time_ist,
      reason_for_disruption,
    } = validatedData

    // Insert new uptime report
    const newReport = await queryInsert(
      `INSERT INTO uptime_reports 
       (property_id, date, downtime_duration, uptime_percentage,
        event_time_ist, reason_for_disruption) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING id, property_id, date, downtime_duration, uptime_percentage,
                 event_time_ist, reason_for_disruption, created_at, updated_at`,
      [
        property_id,
        date,
        downtime_duration,
        uptime_percentage,
        event_time_ist,
        reason_for_disruption,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newReport,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating uptime report:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create uptime report' },
      { status: 500 }
    )
  }
}
