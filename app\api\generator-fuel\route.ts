import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createGeneratorFuelSchema = z.object({
  property_id: z.string().uuid(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  starting_reading: z.number(),
  ending_reading: z.number(),
  fuel_in_generator_percentage: z.number().min(0).max(100),
  fuel_in_tank_liters: z.number().min(0),
})

// GET /api/generator-fuel - Get all generator fuel records
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propertyId = searchParams.get('property_id')
    const limit = searchParams.get('limit')

    let query = `
      SELECT id, property_id, date, starting_reading, ending_reading, 
             fuel_in_generator_percentage, fuel_in_tank_liters,
             created_at, updated_at 
      FROM generator_fuel_updates
    `
    const params: any[] = []

    if (propertyId) {
      query += ' WHERE property_id = $1'
      params.push(propertyId)
    }

    query += ' ORDER BY date DESC, created_at DESC'

    if (limit) {
      const limitIndex = params.length + 1
      query += ` LIMIT $${limitIndex}`
      params.push(parseInt(limit))
    }

    const records = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: records,
    })

  } catch (error) {
    console.error('Error fetching generator fuel records:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch generator fuel records' },
      { status: 500 }
    )
  }
}

// POST /api/generator-fuel - Create a new generator fuel record
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createGeneratorFuelSchema.parse(body)
    const {
      property_id,
      date,
      starting_reading,
      ending_reading,
      fuel_in_generator_percentage,
      fuel_in_tank_liters,
    } = validatedData

    // Insert new generator fuel record
    const newRecord = await queryInsert(
      `INSERT INTO generator_fuel_updates 
       (property_id, date, starting_reading, ending_reading, 
        fuel_in_generator_percentage, fuel_in_tank_liters) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING id, property_id, date, starting_reading, ending_reading, 
                 fuel_in_generator_percentage, fuel_in_tank_liters,
                 created_at, updated_at`,
      [
        property_id,
        date,
        starting_reading,
        ending_reading,
        fuel_in_generator_percentage,
        fuel_in_tank_liters,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newRecord,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating generator fuel record:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create generator fuel record' },
      { status: 500 }
    )
  }
}
