/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // API-only configuration
  experimental: {
    appDir: true,
  },
  // Disable static optimization for API routes
  output: 'standalone',
  // Remove image optimization since we're API-only
  images: {
    unoptimized: true,
  },
}

export default nextConfig
