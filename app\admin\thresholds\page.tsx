import { Suspense } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ThresholdManagement } from "@/components/threshold-management"
import { Breadcrumb } from "@/components/breadcrumb"

export default function ThresholdsPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <Breadcrumb
        items={[
          { label: "Admin", href: "/admin" },
          { label: "Threshold Configuration", href: "/admin/thresholds" },
        ]}
      />

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Threshold Configuration</CardTitle>
            <CardDescription>
              Configure status thresholds for different functional areas across properties. These thresholds determine
              when systems show green (healthy), orange (warning), or red (critical) status indicators.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>Loading threshold configurations...</div>}>
              <ThresholdManagement />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
