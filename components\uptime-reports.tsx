"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  ChevronDown,
  ChevronUp,
  Activity,
  Clock,
  AlertTriangle,
  CheckCircle,
  Calendar,
  CalendarDays,
} from "lucide-react"
import { getUptimeReports, getUptimeStatistics, type UptimeReport } from "@/app/actions/uptime-reports"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

interface UptimeReportsProps {
  propertyId: string
}

export function UptimeReports({ propertyId }: UptimeReportsProps) {
  const [reports, setReports] = useState<UptimeReport[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    fetchData()
  }, [propertyId])

  const fetchData = async () => {
    setLoading(true)
    try {
      const [reportsData, statsData] = await Promise.all([
        getUptimeReports(propertyId),
        getUptimeStatistics(propertyId),
      ])
      setReports(reportsData)
      setStatistics(statsData)
    } catch (error) {
      console.error("Error fetching uptime data:", error)
    } finally {
      setLoading(false)
    }
  }

  const getUptimeStatus = (uptimePercentage: string | null) => {
    if (!uptimePercentage) return "green"
    const percentage = Number.parseFloat(uptimePercentage.replace("%", ""))
    if (percentage >= 99.5) return "green"
    if (percentage >= 95) return "orange"
    return "red"
  }

  const getStatusBadge = (uptimePercentage: string | null) => {
    const status = getUptimeStatus(uptimePercentage)
    const percentage = uptimePercentage || "100%"

    switch (status) {
      case "green":
        return <Badge className="bg-green-100 text-green-800 border-green-200">{percentage}</Badge>
      case "orange":
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">{percentage}</Badge>
      case "red":
        return <Badge className="bg-red-100 text-red-800 border-red-200">{percentage}</Badge>
      default:
        return <Badge variant="outline">{percentage}</Badge>
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading uptime data...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Network Uptime Report
            </CardTitle>
            <CardDescription>Detailed uptime statistics and disruption history</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Statistics Summary */}
        {statistics && (
          <div className="space-y-6 mb-6">
            {/* Weekly Statistics */}
            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-3 flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Weekly Statistics (Last 7 Days)
              </h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="flex items-center justify-center mb-1">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-xs text-slate-600">Avg Uptime</span>
                  </div>
                  <p className="text-lg font-semibold text-green-600">{statistics.weekly.averageUptime}%</p>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="flex items-center justify-center mb-1">
                    <Clock className="h-4 w-4 text-blue-500 mr-1" />
                    <span className="text-xs text-slate-600">Downtime</span>
                  </div>
                  <p className="text-lg font-semibold text-blue-600">{statistics.weekly.totalDowntime} mins</p>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="flex items-center justify-center mb-1">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mr-1" />
                    <span className="text-xs text-slate-600">Disruptions</span>
                  </div>
                  <p className="text-lg font-semibold text-orange-600">{statistics.weekly.disruptionCount}</p>
                </div>
              </div>
            </div>

            {/* Monthly Statistics */}
            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-3 flex items-center">
                <CalendarDays className="h-4 w-4 mr-2" />
                Monthly Statistics (Last 30 Days)
              </h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-green-50 rounded-lg border border-green-100">
                  <div className="flex items-center justify-center mb-1">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-xs text-slate-600">Avg Uptime</span>
                  </div>
                  <p className="text-lg font-semibold text-green-600">{statistics.monthly.averageUptime}%</p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg border border-green-100">
                  <div className="flex items-center justify-center mb-1">
                    <Clock className="h-4 w-4 text-blue-500 mr-1" />
                    <span className="text-xs text-slate-600">Downtime</span>
                  </div>
                  <p className="text-lg font-semibold text-blue-600">{statistics.monthly.totalDowntime} mins</p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg border border-green-100">
                  <div className="flex items-center justify-center mb-1">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mr-1" />
                    <span className="text-xs text-slate-600">Disruptions</span>
                  </div>
                  <p className="text-lg font-semibold text-orange-600">{statistics.monthly.disruptionCount}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Collapsible Table */}
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full">
              {isOpen ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-2" />
                  Hide Detailed Report
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-2" />
                  Show Detailed Report ({reports.length} days)
                </>
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Uptime</TableHead>
                    <TableHead>Downtime</TableHead>
                    <TableHead>Event Time (IST)</TableHead>
                    <TableHead>Reason</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell className="font-medium">{report.date}</TableCell>
                      <TableCell>{getStatusBadge(report.uptime_percentage)}</TableCell>
                      <TableCell>
                        <span
                          className={`${
                            report.downtime_duration === "0 mins" || !report.downtime_duration
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {report.downtime_duration || "0 mins"}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`${
                            report.event_time_ist === "No Disruption" ? "text-green-600" : "text-orange-600"
                          }`}
                        >
                          {report.event_time_ist || "No Disruption"}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`text-sm ${
                            report.reason_for_disruption === "No Disruption" ? "text-green-600" : "text-slate-600"
                          }`}
                        >
                          {report.reason_for_disruption || "No Disruption"}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {reports.length === 0 && (
          <div className="text-center py-8 text-slate-500">
            <Activity className="h-12 w-12 mx-auto mb-4 text-slate-300" />
            <p>No uptime data available for this property</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
