import { requireRole } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Shield, Lock, Database, Settings } from "lucide-react"
import Link from "next/link"
import { RoleBasedUI } from "@/components/role-based-ui"

export default async function AdminDashboardPage() {
  // Ensure only admins can access this page
  await requireRole(["admin"])

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Admin Dashboard</h1>

      <div className="grid gap-6 md:grid-cols-3">
        <Link href="/admin/users">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Management
              </CardTitle>
              <CardDescription>Manage user accounts and roles</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-slate-500">Activate/deactivate users and assign roles to control access.</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/roles">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role Management
              </CardTitle>
              <CardDescription>Manage system roles</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-slate-500">Create, edit, and delete roles to organize user permissions.</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/permissions">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Permission Management
              </CardTitle>
              <CardDescription>Manage URL permissions</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-slate-500">
                Control which roles can access specific URL patterns in the system.
              </p>
            </CardContent>
          </Card>
        </Link>

        <RoleBasedUI roles="system_admin">
          <Link href="/admin/database">
            <Card className="h-full transition-all hover:shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Database Management
                </CardTitle>
                <CardDescription>Advanced database operations</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-slate-500">
                  Perform database maintenance, backups, and advanced operations.
                </p>
              </CardContent>
            </Card>
          </Link>
        </RoleBasedUI>

        <RoleBasedUI roles="system_admin">
          <Link href="/admin/settings">
            <Card className="h-full transition-all hover:shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  System Settings
                </CardTitle>
                <CardDescription>Configure system settings</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-slate-500">
                  Manage global system settings, integrations, and advanced configurations.
                </p>
              </CardContent>
            </Card>
          </Link>
        </RoleBasedUI>
      </div>
    </div>
  )
}
