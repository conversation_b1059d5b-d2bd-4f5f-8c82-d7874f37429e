"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, Trash2, Loader2, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { addSiteMember, updateSiteMember, deleteSiteMember, type SiteMember } from "@/app/actions/site-management"

interface SiteMembersManagementProps {
  siteId: string
  siteName: string
  initialMembers: SiteMember[]
}

export function SiteMembersManagement({ siteId, siteName, initialMembers }: SiteMembersManagementProps) {
  const [members, setMembers] = useState<SiteMember[]>(initialMembers)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formError, setFormError] = useState("")
  const [memberToEdit, setMemberToEdit] = useState<SiteMember | null>(null)
  const [newMember, setNewMember] = useState({
    name: "",
    mobile_number: "",
    role: "",
    duty_time: "",
  })
  const { toast } = useToast()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setNewMember((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewMember((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleAddMember = async () => {
    setFormError("")
    setIsSubmitting(true)

    // Validate form
    if (!newMember.name || !newMember.role) {
      setFormError("Name and Role are required")
      setIsSubmitting(false)
      return
    }

    try {
      const result = await addSiteMember({
        site_id: siteId,
        name: newMember.name,
        mobile_number: newMember.mobile_number || "",
        role: newMember.role,
        duty_time: newMember.duty_time || "",
      })

      if (result.success && result.data) {
        setMembers((prev) => [...prev, result.data as SiteMember])
        setNewMember({
          name: "",
          mobile_number: "",
          role: "",
          duty_time: "",
        })

        toast({
          title: "Member Added",
          description: `${newMember.name} has been added to ${siteName}.`,
        })

        setIsAddDialogOpen(false)
      } else {
        setFormError(result.error || "Failed to add member")
      }
    } catch (error) {
      setFormError((error as Error).message || "An error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEditMember = async () => {
    if (!memberToEdit) return

    setFormError("")
    setIsSubmitting(true)

    // Validate form
    if (!memberToEdit.name || !memberToEdit.role) {
      setFormError("Name and Role are required")
      setIsSubmitting(false)
      return
    }

    try {
      const result = await updateSiteMember(memberToEdit)

      if (result.success) {
        setMembers((prev) => prev.map((member) => (member.id === memberToEdit.id ? memberToEdit : member)))

        toast({
          title: "Member Updated",
          description: `${memberToEdit.name} has been updated.`,
        })

        setIsEditDialogOpen(false)
        setMemberToEdit(null)
      } else {
        setFormError(result.error || "Failed to update member")
      }
    } catch (error) {
      setFormError((error as Error).message || "An error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleRemoveMember = async (id: string, name: string) => {
    try {
      const result = await deleteSiteMember(id, siteId)

      if (result.success) {
        setMembers((prev) => prev.filter((member) => member.id !== id))

        toast({
          title: "Member Removed",
          description: `${name} has been removed from ${siteName}.`,
        })
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.error || "Failed to remove member",
        })
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "An error occurred",
      })
    }
  }

  const openEditDialog = (member: SiteMember) => {
    setMemberToEdit({ ...member })
    setIsEditDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{siteName} Members</h3>
          <p className="text-sm text-muted-foreground">Manage members for this site</p>
        </div>

        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Member
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Member</DialogTitle>
              <DialogDescription>
                Add a new member to {siteName}. They will be included in the attendance records.
              </DialogDescription>
            </DialogHeader>

            {formError && (
              <Alert variant="destructive">
                <AlertDescription>{formError}</AlertDescription>
              </Alert>
            )}

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={newMember.name}
                  onChange={handleInputChange}
                  placeholder="Enter full name"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="mobile_number">Mobile Number</Label>
                <Input
                  id="mobile_number"
                  name="mobile_number"
                  value={newMember.mobile_number}
                  onChange={handleInputChange}
                  placeholder="Enter mobile number"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Select value={newMember.role} onValueChange={(value) => handleSelectChange("role", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Supervisor">Supervisor</SelectItem>
                    <SelectItem value="Mason">Mason</SelectItem>
                    <SelectItem value="Helper">Helper</SelectItem>
                    <SelectItem value="Carpenter">Carpenter</SelectItem>
                    <SelectItem value="Electrician">Electrician</SelectItem>
                    <SelectItem value="Plumber">Plumber</SelectItem>
                    <SelectItem value="Painter">Painter</SelectItem>
                    <SelectItem value="Security Guard">Security Guard</SelectItem>
                    <SelectItem value="Gardener">Gardener</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="duty_time">Duty Time</Label>
                <Input
                  id="duty_time"
                  name="duty_time"
                  value={newMember.duty_time}
                  onChange={handleInputChange}
                  placeholder="e.g., 09:00 - 18:00"
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddMember} disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  "Add Member"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Mobile Number</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Duty Time</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.length > 0 ? (
              members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">{member.name}</TableCell>
                  <TableCell>{member.mobile_number}</TableCell>
                  <TableCell>{member.role}</TableCell>
                  <TableCell>{member.duty_time}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(member)}
                        className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveMember(member.id, member.name)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  No members found for this site
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Member Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Member</DialogTitle>
            <DialogDescription>Update member information for {siteName}.</DialogDescription>
          </DialogHeader>

          {formError && (
            <Alert variant="destructive">
              <AlertDescription>{formError}</AlertDescription>
            </Alert>
          )}

          {memberToEdit && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Full Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={memberToEdit.name}
                  onChange={(e) => setMemberToEdit({ ...memberToEdit, name: e.target.value })}
                  placeholder="Enter full name"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-mobile">Mobile Number</Label>
                <Input
                  id="edit-mobile"
                  name="mobile_number"
                  value={memberToEdit.mobile_number}
                  onChange={(e) => setMemberToEdit({ ...memberToEdit, mobile_number: e.target.value })}
                  placeholder="Enter mobile number"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-role">Role</Label>
                <Select
                  value={memberToEdit.role}
                  onValueChange={(value) => setMemberToEdit({ ...memberToEdit, role: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Supervisor">Supervisor</SelectItem>
                    <SelectItem value="Mason">Mason</SelectItem>
                    <SelectItem value="Helper">Helper</SelectItem>
                    <SelectItem value="Carpenter">Carpenter</SelectItem>
                    <SelectItem value="Electrician">Electrician</SelectItem>
                    <SelectItem value="Plumber">Plumber</SelectItem>
                    <SelectItem value="Painter">Painter</SelectItem>
                    <SelectItem value="Security Guard">Security Guard</SelectItem>
                    <SelectItem value="Gardener">Gardener</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-dutyTime">Duty Time</Label>
                <Input
                  id="edit-dutyTime"
                  name="duty_time"
                  value={memberToEdit.duty_time}
                  onChange={(e) => setMemberToEdit({ ...memberToEdit, duty_time: e.target.value })}
                  placeholder="e.g., 09:00 - 18:00"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditMember} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Member"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
