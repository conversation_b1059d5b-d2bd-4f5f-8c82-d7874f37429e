import { createServerClient } from "@/lib/supabase"
import { NextResponse } from "next/server"
import { revalidatePath } from "next/cache"

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id
    if (!id) {
      return NextResponse.json({ error: "Missing ID parameter" }, { status: 400 })
    }

    const data = await request.json()

    // Validate the data
    const { date, starting_reading, ending_reading, fuel_in_generator_percentage, fuel_in_tank_liters } = data

    if (
      !date ||
      starting_reading === undefined ||
      ending_reading === undefined ||
      fuel_in_generator_percentage === undefined ||
      fuel_in_tank_liters === undefined
    ) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Update the record in Supabase
    const supabase = createServerClient()
    const { error, data: updatedData } = await supabase
      .from("generator_fuel_updates")
      .update({
        date,
        starting_reading,
        ending_reading,
        fuel_in_generator_percentage,
        fuel_in_tank_liters,
      })
      .eq("id", id)
      .select()

    if (error) {
      console.error("Error updating generator fuel:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Get the property_id to revalidate the correct path
    const { data: fuelUpdate } = await supabase
      .from("generator_fuel_updates")
      .select("property_id")
      .eq("id", id)
      .single()

    if (fuelUpdate?.property_id) {
      revalidatePath(`/dashboard/home/<USER>/electricity`)
    }

    return NextResponse.json({
      message: "Generator fuel update successful",
      data: updatedData,
    })
  } catch (error) {
    console.error("Error in PATCH /api/generator-fuel/[id]:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id
    if (!id) {
      return NextResponse.json({ error: "Missing ID parameter" }, { status: 400 })
    }

    // Get the property_id before deleting the record
    const supabase = createServerClient()
    const { data: fuelUpdate, error: fetchError } = await supabase
      .from("generator_fuel_updates")
      .select("property_id")
      .eq("id", id)
      .single()

    if (fetchError) {
      console.error("Error fetching generator fuel update:", fetchError)
      return NextResponse.json({ error: fetchError.message }, { status: 500 })
    }

    // Delete the record
    const { error } = await supabase.from("generator_fuel_updates").delete().eq("id", id)

    if (error) {
      console.error("Error deleting generator fuel update:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Revalidate the path if we have the property_id
    if (fuelUpdate?.property_id) {
      revalidatePath(`/dashboard/home/<USER>/electricity`)
    }

    return NextResponse.json({
      message: "Generator fuel update deleted successfully",
    })
  } catch (error) {
    console.error("Error in DELETE /api/generator-fuel/[id]:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
