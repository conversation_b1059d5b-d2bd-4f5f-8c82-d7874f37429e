import { NextRequest, NextResponse } from 'next/server'
import { queryOne, db } from '@/lib/database'
import { z } from 'zod'

const updateGeneratorFuelSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  starting_reading: z.number(),
  ending_reading: z.number(),
  fuel_in_generator_percentage: z.number().min(0).max(100),
  fuel_in_tank_liters: z.number().min(0),
})

// GET /api/generator-fuel/[id] - Get a specific generator fuel record
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const record = await queryOne(
      'SELECT * FROM generator_fuel_updates WHERE id = $1',
      [id]
    )

    if (!record) {
      return NextResponse.json(
        { success: false, error: 'Generator fuel record not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: record,
    })

  } catch (error) {
    console.error('Error fetching generator fuel record:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch generator fuel record' },
      { status: 500 }
    )
  }
}

// PUT /api/generator-fuel/[id] - Update a specific generator fuel record
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    // Validate input
    const validatedData = updateGeneratorFuelSchema.parse(body)
    const { date, starting_reading, ending_reading, fuel_in_generator_percentage, fuel_in_tank_liters } = validatedData

    // Check if record exists
    const existingRecord = await queryOne('SELECT id FROM generator_fuel_updates WHERE id = $1', [id])
    if (!existingRecord) {
      return NextResponse.json(
        { success: false, error: 'Generator fuel record not found' },
        { status: 404 }
      )
    }

    // Update the record
    const result = await db.query(
      `UPDATE generator_fuel_updates
       SET date = $1, starting_reading = $2, ending_reading = $3,
           fuel_in_generator_percentage = $4, fuel_in_tank_liters = $5,
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $6
       RETURNING *`,
      [date, starting_reading, ending_reading, fuel_in_generator_percentage, fuel_in_tank_liters, id]
    )

    const updatedRecord = result.rows[0]

    return NextResponse.json({
      success: true,
      data: updatedRecord,
    })

  } catch (error) {
    console.error('Error updating generator fuel record:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update generator fuel record' },
      { status: 500 }
    )
  }
}

// DELETE /api/generator-fuel/[id] - Delete a specific generator fuel record
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if record exists
    const existingRecord = await queryOne('SELECT id FROM generator_fuel_updates WHERE id = $1', [id])
    if (!existingRecord) {
      return NextResponse.json(
        { success: false, error: 'Generator fuel record not found' },
        { status: 404 }
      )
    }

    // Delete the record
    await db.query('DELETE FROM generator_fuel_updates WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'Generator fuel record deleted successfully',
    })

  } catch (error) {
    console.error('Error deleting generator fuel record:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete generator fuel record' },
      { status: 500 }
    )
  }
}
