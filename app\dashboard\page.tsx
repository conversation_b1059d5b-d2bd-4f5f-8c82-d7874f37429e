"use client"
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Home, Building2, Monitor, ArrowRight } from "lucide-react"
import Link from "next/link"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { PageNavigation } from "@/components/page-navigation"
// Import the RoleDebugger component
// import { RoleDebugger } from "@/components/role-debugger"

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-slate-50">
      {/* Remove the RoleDebugger component */}
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <h1 className="mb-8 text-3xl font-bold">SRSR Property Management</h1>

        <div className="grid gap-6 md:grid-cols-3">
          <Link href="/dashboard/home" className="block">
            <Card className="h-full transition-all hover:shadow-md">
              <CardHeader className="flex flex-row items-center gap-4">
                <Home className="h-8 w-8 text-slate-700" />
                <div>
                  <CardTitle>Home</CardTitle>
                  <CardDescription>Manage residential properties</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="flex justify-end">
                <ArrowRight className="h-5 w-5 text-slate-400" />
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/office" className="block">
            <Card className="h-full transition-all hover:shadow-md">
              <CardHeader className="flex flex-row items-center gap-4">
                <Building2 className="h-8 w-8 text-slate-700" />
                <div>
                  <CardTitle>Office</CardTitle>
                  <CardDescription>Manage office spaces and sites</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="flex justify-end">
                <ArrowRight className="h-5 w-5 text-slate-400" />
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/status" className="block">
            <Card className="h-full transition-all hover:shadow-md">
              <CardHeader className="flex flex-row items-center gap-4">
                <Monitor className="h-8 w-8 text-slate-700" />
                <div>
                  <CardTitle>Current Statuses</CardTitle>
                  <CardDescription>Real-time overview of all property systems</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="flex justify-end">
                <ArrowRight className="h-5 w-5 text-slate-400" />
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}
