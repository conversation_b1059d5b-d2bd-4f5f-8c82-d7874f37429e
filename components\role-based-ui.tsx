"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { getBrowserClient } from "@/lib/supabase"

interface RoleBasedUIProps {
  roles: string | string[]
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function RoleBasedUI({ roles, children, fallback = null }: RoleBasedUIProps) {
  const [isAdmin1, setIsAdmin1] = useState(false)
  const [hasRequiredRole, setHasRequiredRole] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function checkUserRole() {
      try {
        const supabase = getBrowserClient()

        // Check if user is admin1
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("username, role")
          .limit(1)
          .single()

        if (!userError && userData) {
          // If username is admin1, always show content
          if (userData.username === "admin1") {
            setIsAdmin1(true)
            setHasRequiredRole(true)
            setLoading(false)
            return
          }

          // If user has admin role, always show content
          if (userData.role === "admin") {
            setHasRequiredRole(true)
            setLoading(false)
            return
          }

          // Otherwise, check user roles
          const roleArray = Array.isArray(roles) ? roles : [roles]

          // Check if user's role matches any required role
          if (roleArray.includes(userData.role)) {
            setHasRequiredRole(true)
            setLoading(false)
            return
          }

          // Check user_roles table
          const { data: userRolesData, error: userRolesError } = await supabase
            .from("user_roles")
            .select("roles(name)")
            .eq("user_id", userData.id)

          if (!userRolesError && userRolesData) {
            const userRoles = userRolesData.map((ur) => ur.roles?.name).filter(Boolean) as string[]

            // Check if user has any of the required roles
            if (userRoles.some((role) => roleArray.includes(role))) {
              setHasRequiredRole(true)
            }
          }
        }
      } catch (error) {
        console.error("Error checking user role:", error)
      } finally {
        setLoading(false)
      }
    }

    checkUserRole()
  }, [roles])

  // While loading, don't render anything to prevent flashing
  if (loading) {
    return null
  }

  // If user is admin1 or has the required role, render the children
  if (isAdmin1 || hasRequiredRole) {
    return <>{children}</>
  }

  // Otherwise, render the fallback
  return <>{fallback}</>
}
