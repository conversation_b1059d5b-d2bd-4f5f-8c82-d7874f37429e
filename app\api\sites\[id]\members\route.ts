import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert, queryOne } from '@/lib/database'
import { z } from 'zod'

const createMemberSchema = z.object({
  name: z.string().min(1).max(255),
  mobile_number: z.string().max(20).optional(),
  role: z.string().max(100).optional(),
  duty_time: z.string().max(100).optional(),
})

// GET /api/sites/[id]/members - Get all members for a site
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: siteId } = params

    // Check if site exists
    const site = await queryOne('SELECT id FROM sites WHERE id = $1', [siteId])
    if (!site) {
      return NextResponse.json(
        { success: false, error: 'Site not found' },
        { status: 404 }
      )
    }

    const members = await queryMany(
      `SELECT id, name, mobile_number, site_id, role, duty_time, created_at, updated_at 
       FROM site_members 
       WHERE site_id = $1 
       ORDER BY name`,
      [siteId]
    )

    return NextResponse.json({
      success: true,
      data: members,
    })

  } catch (error) {
    console.error('Error fetching site members:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch site members' },
      { status: 500 }
    )
  }
}

// POST /api/sites/[id]/members - Create a new member for a site
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: siteId } = params
    const body = await request.json()
    
    // Validate input
    const validatedData = createMemberSchema.parse(body)
    const { name, mobile_number, role, duty_time } = validatedData

    // Check if site exists
    const site = await queryOne('SELECT id FROM sites WHERE id = $1', [siteId])
    if (!site) {
      return NextResponse.json(
        { success: false, error: 'Site not found' },
        { status: 404 }
      )
    }

    // Insert new member
    const newMember = await queryInsert(
      `INSERT INTO site_members (name, mobile_number, site_id, role, duty_time) 
       VALUES ($1, $2, $3, $4, $5) 
       RETURNING id, name, mobile_number, site_id, role, duty_time, created_at, updated_at`,
      [name, mobile_number, siteId, role, duty_time]
    )

    return NextResponse.json({
      success: true,
      data: newMember,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating site member:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create site member' },
      { status: 500 }
    )
  }
}
