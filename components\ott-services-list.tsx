"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Eye, EyeOff, Edit, Trash2, Plus } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { type OttService, deleteOttService } from "@/app/actions/ott-services"
import { OttServiceForm } from "./ott-service-form"
import { Badge } from "@/components/ui/badge"
import { ConfirmationDialog } from "./confirmation-dialog"

interface OttServicesListProps {
  services: OttService[]
  propertyId: string
  onRefresh: () => void
}

export function OttServicesList({ services, propertyId, onRefresh }: OttServicesListProps) {
  const [selectedService, setSelectedService] = useState<OttService | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteError, setDeleteError] = useState("")
  const [visiblePasswords, setVisiblePasswords] = useState<Record<string, boolean>>({})

  const handleDelete = async (id: string) => {
    setDeleteLoading(true)
    setDeleteError("")

    try {
      const result = await deleteOttService(id, propertyId)
      if (result.success) {
        setIsDeleteDialogOpen(false)
        onRefresh()
      } else {
        setDeleteError(result.error || "An error occurred")
      }
    } catch (err) {
      setDeleteError("An unexpected error occurred")
      console.error(err)
    } finally {
      setDeleteLoading(false)
    }
  }

  const togglePasswordVisibility = (id: string) => {
    setVisiblePasswords((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "-"

    // Check if the date is in YYYY-MM-DD format
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const date = new Date(dateString)
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      })
    }

    // If it's not in the expected format, return as is
    return dateString
  }

  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            Active
          </Badge>
        )
      case "expired":
        return (
          <Badge variant="secondary" className="bg-red-100 text-red-800">
            Expired
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            Pending
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-xl">OTT Services</CardTitle>
          <CardDescription>Manage OTT platform subscriptions</CardDescription>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Service
        </Button>
      </CardHeader>
      <CardContent>
        {services.length === 0 ? (
          <div className="rounded-md bg-slate-50 p-8 text-center">
            <p className="text-slate-500">No OTT services found</p>
            <Button variant="outline" className="mt-4" onClick={() => onRefresh()}>
              Refresh
            </Button>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Platform</TableHead>
                  <TableHead>Plan & Duration</TableHead>
                  <TableHead>Login</TableHead>
                  <TableHead>Password</TableHead>
                  <TableHead>Next Payment</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {services.map((service) => (
                  <TableRow key={service.id}>
                    <TableCell className="font-medium">{service.platform}</TableCell>
                    <TableCell>{service.plan_and_duration}</TableCell>
                    <TableCell>{service.username}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className="font-mono">
                          {visiblePasswords[service.id] ? service.password : "••••••••"}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => togglePasswordVisibility(service.id)}
                        >
                          {visiblePasswords[service.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(service.next_recharge_date)}</TableCell>
                    <TableCell>{getStatusBadge(service.current_status)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedService(service)
                            setIsEditDialogOpen(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            setSelectedService(service)
                            setIsDeleteDialogOpen(true)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Edit Service Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Edit OTT Service</DialogTitle>
              <DialogDescription>Update the OTT service details</DialogDescription>
            </DialogHeader>
            {selectedService && (
              <OttServiceForm
                propertyId={propertyId}
                existingService={selectedService}
                onSuccess={() => {
                  setIsEditDialogOpen(false)
                  onRefresh()
                }}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Add Service Dialog */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Add OTT Service</DialogTitle>
              <DialogDescription>Add a new OTT service subscription</DialogDescription>
            </DialogHeader>
            <OttServiceForm
              propertyId={propertyId}
              onSuccess={() => {
                setIsAddDialogOpen(false)
                onRefresh()
              }}
            />
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <ConfirmationDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          title="Confirm Deletion"
          description="Are you sure you want to delete this OTT service? This action cannot be undone."
          onConfirm={() => selectedService && handleDelete(selectedService.id)}
          loading={deleteLoading}
        />
      </CardContent>
    </Card>
  )
}
