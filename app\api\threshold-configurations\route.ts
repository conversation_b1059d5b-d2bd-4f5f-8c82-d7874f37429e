import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createThresholdConfigSchema = z.object({
  functional_area: z.string().min(1).max(100),
  metric_name: z.string().min(1).max(100),
  green_min: z.number().optional(),
  green_max: z.number().optional(),
  orange_min: z.number().optional(),
  orange_max: z.number().optional(),
  red_min: z.number().optional(),
  red_max: z.number().optional(),
  unit: z.string().max(20).optional(),
  description: z.string().optional(),
  property_type: z.string().max(50).default('all'),
})

// GET /api/threshold-configurations - Get all threshold configurations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const functionalArea = searchParams.get('functional_area')
    const propertyType = searchParams.get('property_type')

    let query = `
      SELECT id, functional_area, metric_name, green_min, green_max, orange_min, orange_max,
             red_min, red_max, unit, description, property_type, created_at, updated_at 
      FROM threshold_configurations
    `
    const params: any[] = []
    const conditions: string[] = []

    if (functionalArea) {
      conditions.push(`functional_area = $${params.length + 1}`)
      params.push(functionalArea)
    }

    if (propertyType) {
      conditions.push(`property_type = $${params.length + 1}`)
      params.push(propertyType)
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    query += ' ORDER BY functional_area, metric_name'

    const configs = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: configs,
    })

  } catch (error) {
    console.error('Error fetching threshold configurations:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch threshold configurations' },
      { status: 500 }
    )
  }
}

// POST /api/threshold-configurations - Create a new threshold configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createThresholdConfigSchema.parse(body)
    const {
      functional_area,
      metric_name,
      green_min,
      green_max,
      orange_min,
      orange_max,
      red_min,
      red_max,
      unit,
      description,
      property_type,
    } = validatedData

    // Insert new threshold configuration
    const newConfig = await queryInsert(
      `INSERT INTO threshold_configurations 
       (functional_area, metric_name, green_min, green_max, orange_min, orange_max,
        red_min, red_max, unit, description, property_type) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) 
       RETURNING id, functional_area, metric_name, green_min, green_max, orange_min, orange_max,
                 red_min, red_max, unit, description, property_type, created_at, updated_at`,
      [
        functional_area,
        metric_name,
        green_min,
        green_max,
        orange_min,
        orange_max,
        red_min,
        red_max,
        unit,
        description,
        property_type,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newConfig,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating threshold configuration:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create threshold configuration' },
      { status: 500 }
    )
  }
}
