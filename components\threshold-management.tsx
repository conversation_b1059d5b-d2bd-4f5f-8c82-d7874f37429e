"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Edit, Trash2, Settings } from "lucide-react"
import { ThresholdForm } from "./threshold-form"
import { ThresholdPreview } from "./threshold-preview"
import {
  getThresholdConfigurations,
  deleteThresholdConfiguration,
  type ThresholdConfig,
} from "@/app/actions/threshold-config"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export function ThresholdManagement() {
  const [thresholds, setThresholds] = useState<ThresholdConfig[]>([])
  const [loading, setLoading] = useState(true)
  const [editingThreshold, setEditingThreshold] = useState<ThresholdConfig | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  useEffect(() => {
    loadThresholds()
  }, [])

  const loadThresholds = async () => {
    try {
      const data = await getThresholdConfigurations()
      setThresholds(data)
    } catch (error) {
      console.error("Failed to load thresholds:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: number) => {
    try {
      await deleteThresholdConfiguration(id)
      await loadThresholds()
    } catch (error) {
      console.error("Failed to delete threshold:", error)
    }
  }

  const handleEdit = (threshold: ThresholdConfig) => {
    setEditingThreshold(threshold)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingThreshold(null)
    loadThresholds()
  }

  const getStatusColor = (level: "green" | "orange" | "red") => {
    switch (level) {
      case "green":
        return "bg-green-100 text-green-800"
      case "orange":
        return "bg-orange-100 text-orange-800"
      case "red":
        return "bg-red-100 text-red-800"
    }
  }

  const groupedThresholds = thresholds.reduce(
    (acc, threshold) => {
      if (!acc[threshold.functional_area]) {
        acc[threshold.functional_area] = []
      }
      acc[threshold.functional_area].push(threshold)
      return acc
    },
    {} as Record<string, ThresholdConfig[]>,
  )

  if (loading) {
    return <div>Loading threshold configurations...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Threshold Configurations</h3>
        <div className="flex gap-2">
          <Dialog open={showPreview} onOpenChange={setShowPreview}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Preview
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>Threshold Preview</DialogTitle>
                <DialogDescription>
                  Test how different values would be classified with current thresholds
                </DialogDescription>
              </DialogHeader>
              <ThresholdPreview thresholds={thresholds} />
            </DialogContent>
          </Dialog>

          <Dialog open={showForm} onOpenChange={setShowForm}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Threshold
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingThreshold ? "Edit Threshold" : "Add New Threshold"}</DialogTitle>
                <DialogDescription>Configure status thresholds for functional areas</DialogDescription>
              </DialogHeader>
              <ThresholdForm threshold={editingThreshold} onClose={handleFormClose} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid gap-6">
        {Object.entries(groupedThresholds).map(([functionalArea, areaThresholds]) => (
          <Card key={functionalArea}>
            <CardHeader>
              <CardTitle className="capitalize">{functionalArea.replace("_", " ")}</CardTitle>
              <CardDescription>Thresholds for {functionalArea} monitoring</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {areaThresholds.map((threshold) => (
                  <div key={threshold.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{threshold.metric_name.replace("_", " ")}</h4>
                        <Badge variant="outline">{threshold.property_type}</Badge>
                        {threshold.unit && <Badge variant="secondary">{threshold.unit}</Badge>}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{threshold.description}</p>
                      <div className="flex gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 rounded-full bg-green-500"></div>
                          <span>
                            {threshold.green_min !== null && threshold.green_max !== null
                              ? `${threshold.green_min} - ${threshold.green_max}`
                              : "N/A"}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                          <span>
                            {threshold.orange_min !== null && threshold.orange_max !== null
                              ? `${threshold.orange_min} - ${threshold.orange_max}`
                              : "N/A"}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 rounded-full bg-red-500"></div>
                          <span>
                            {threshold.red_min !== null && threshold.red_max !== null
                              ? `${threshold.red_min} - ${threshold.red_max}`
                              : "N/A"}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => handleEdit(threshold)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Threshold</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete this threshold configuration? This action cannot be
                              undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => threshold.id && handleDelete(threshold.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
