"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, Check, Plus, Trash2, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { getRoles } from "@/app/actions/roles"
import { getPermissions, createPermission, deletePermission } from "@/app/actions/permissions"
import type { Role, Permission } from "@/lib/auth"

export function PermissionManagement() {
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)
  const [urlPattern, setUrlPattern] = useState("")
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch roles and permissions
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        const [rolesData, permissionsData] = await Promise.all([getRoles(), getPermissions()])
        setRoles(rolesData)
        setPermissions(permissionsData)
      } catch (err) {
        setError("Failed to load data")
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Open add dialog
  const openAddDialog = () => {
    setUrlPattern("")
    setSelectedRoleId(null)
    setError("")
    setIsAddDialogOpen(true)
  }

  // Open delete dialog
  const openDeleteDialog = (permission: Permission) => {
    setSelectedPermission(permission)
    setError("")
    setIsDeleteDialogOpen(true)
  }

  // Handle add permission
  const handleAddPermission = async () => {
    if (!urlPattern) {
      setError("URL pattern is required")
      return
    }

    if (!selectedRoleId) {
      setError("Role is required")
      return
    }

    setIsSubmitting(true)
    setError("")

    try {
      const result = await createPermission({
        urlPattern,
        roleId: selectedRoleId,
      })

      if (result.success) {
        setIsAddDialogOpen(false)
        setSuccess("Permission created successfully")

        // Refresh permissions list
        const permissionsData = await getPermissions()
        setPermissions(permissionsData)
      } else {
        setError(result.error || "Failed to create permission")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle delete permission
  const handleDeletePermission = async () => {
    if (!selectedPermission) return

    setIsSubmitting(true)
    setError("")

    try {
      const result = await deletePermission(selectedPermission.id)

      if (result.success) {
        setIsDeleteDialogOpen(false)
        setSuccess("Permission deleted successfully")

        // Refresh permissions list
        const permissionsData = await getPermissions()
        setPermissions(permissionsData)
      } else {
        setError(result.error || "Failed to delete permission")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get role name by ID
  const getRoleName = (roleId: number) => {
    const role = roles.find((r) => r.id === roleId)
    return role ? role.name : "Unknown"
  }

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 text-green-700">
          <Check className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-end">
        <Button onClick={openAddDialog}>
          <Plus className="mr-2 h-4 w-4" />
          Add Permission
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>URL Permissions</CardTitle>
          <CardDescription>Manage which roles can access specific URL patterns</CardDescription>
        </CardHeader>
        <CardContent>
          {permissions.length === 0 ? (
            <div className="rounded-md bg-slate-50 p-8 text-center">
              <p className="text-slate-500">No permissions found</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>URL Pattern</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {permissions.map((permission) => (
                    <TableRow key={permission.id}>
                      <TableCell className="font-medium">{permission.url_pattern}</TableCell>
                      <TableCell>{getRoleName(permission.role_id)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="destructive" size="sm" onClick={() => openDeleteDialog(permission)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Permission Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Permission</DialogTitle>
            <DialogDescription>Create a new URL permission for a role</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="url-pattern">URL Pattern</Label>
              <Input
                id="url-pattern"
                value={urlPattern}
                onChange={(e) => setUrlPattern(e.target.value)}
                placeholder="e.g., /dashboard/* or /admin/users"
              />
              <p className="text-xs text-slate-500">
                Use * as a wildcard. For example, /dashboard/* matches all URLs that start with /dashboard/
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select onValueChange={(value) => setSelectedRoleId(Number(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddPermission} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Permission"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Permission Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Permission</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this permission? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {selectedPermission && (
            <div className="py-4">
              <p className="font-medium">{selectedPermission.url_pattern}</p>
              <p className="text-sm text-slate-500">Role: {getRoleName(selectedPermission.role_id)}</p>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeletePermission} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Permission"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
