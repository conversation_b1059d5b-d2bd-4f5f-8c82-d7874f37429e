"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { History, Edit, Save, X, Trash2, <PERSON>ertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import type { GeneratorFuelUpdate } from "@/app/actions/generator-fuel"

interface GeneratorFuelHistoryProps {
  updates: GeneratorFuelUpdate[]
  onRefresh: () => void
}

export function GeneratorFuelHistory({ updates, onRefresh }: GeneratorFuelHistoryProps) {
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editD<PERSON>, setEditData] = useState<Partial<GeneratorFuelUpdate>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    // Format as YYYY-MM-DD for input type="date"
    const date = new Date(dateString)
    return date.toISOString().split("T")[0]
  }

  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return "-"
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Safe calculation function with null checks
  const calculateStats = (update: GeneratorFuelUpdate) => {
    if (!update) {
      return {
        fuelInGenerator: 0,
        totalFuel: 0,
        powerBackupHours: 0,
      }
    }

    // Generator capacity is 100 liters
    const generatorCapacity = 100

    // Calculate fuel in generator (liters) with null check
    const fuelInGeneratorPercentage = update.fuel_in_generator_percentage || 0
    const fuelInGenerator = (generatorCapacity * fuelInGeneratorPercentage) / 100

    // Calculate total fuel available with null check
    const fuelInTank = update.fuel_in_tank_liters || 0
    const totalFuel = fuelInGenerator + fuelInTank

    // Average fuel consumption is 6.5 liters per hour
    const fuelConsumptionRate = 6.5

    // Calculate power backup hours with check for division by zero
    const powerBackupHours = totalFuel > 0 ? Math.round((totalFuel / fuelConsumptionRate) * 10) / 10 : 0

    return {
      fuelInGenerator,
      totalFuel,
      powerBackupHours,
    }
  }

  const handleEdit = (update: GeneratorFuelUpdate) => {
    if (!update || !update.id) return
    setEditingId(update.id)
    setEditData({
      date: update.date,
      starting_reading: update.starting_reading,
      ending_reading: update.ending_reading,
      fuel_in_generator_percentage: update.fuel_in_generator_percentage,
      fuel_in_tank_liters: update.fuel_in_tank_liters,
    })
    setError(null)
  }

  const handleCancel = () => {
    setEditingId(null)
    setEditData({})
    setError(null)
  }

  const handleChange = (field: string, value: string) => {
    if (field === "date") {
      setEditData((prev) => ({
        ...prev,
        [field]: value,
      }))
    } else {
      setEditData((prev) => ({
        ...prev,
        [field]: Number.parseFloat(value) || 0,
      }))
    }
  }

  const handleSave = async (update: GeneratorFuelUpdate) => {
    if (!update || !update.id) return

    setIsSubmitting(true)
    setError(null)

    try {
      // Create a new object with only the fields we want to update
      const updateData = {
        date: editData.date,
        starting_reading: editData.starting_reading,
        ending_reading: editData.ending_reading,
        fuel_in_generator_percentage: editData.fuel_in_generator_percentage,
        fuel_in_tank_liters: editData.fuel_in_tank_liters,
      }

      // Use fetch to call a server action or API endpoint
      const response = await fetch(`/api/generator-fuel/${update.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to update fuel data")
      }

      // Success - reset state
      setEditingId(null)
      setEditData({})

      // Show success toast
      toast({
        title: "Update successful",
        description: "Fuel data has been updated successfully.",
      })

      // Refresh the data
      onRefresh()
    } catch (err) {
      console.error("Error updating fuel data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")

      // Show error toast
      toast({
        title: "Update failed",
        description: err instanceof Error ? err.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const confirmDelete = (id: number | undefined) => {
    if (!id) return
    setDeleteId(id)
    setDeleteDialogOpen(true)
  }

  const handleDelete = async () => {
    if (!deleteId) return

    setIsDeleting(true)

    try {
      const response = await fetch(`/api/generator-fuel/${deleteId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to delete fuel data")
      }

      // Success - close dialog
      setDeleteDialogOpen(false)
      setDeleteId(null)

      // Show success toast
      toast({
        title: "Delete successful",
        description: "Fuel data entry has been deleted.",
      })

      // Refresh the data
      onRefresh()
    } catch (err) {
      console.error("Error deleting fuel data:", err)

      // Show error toast
      toast({
        title: "Delete failed",
        description: err instanceof Error ? err.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Fuel Update History
          </CardTitle>
          <CardDescription>Recent generator fuel updates</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 rounded-md bg-red-50 p-3 text-red-700 flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              <p>{error}</p>
            </div>
          )}

          {!updates || updates.length === 0 ? (
            <div className="rounded-md bg-slate-50 p-4 text-center">
              <p className="text-slate-500">No fuel updates found</p>
            </div>
          ) : (
            <div className="rounded-md border overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Starting Reading</TableHead>
                    <TableHead>Ending Reading</TableHead>
                    <TableHead>Fuel in Generator</TableHead>
                    <TableHead>Fuel in Tank</TableHead>
                    <TableHead>Backup Hours</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {updates.map((update) => {
                    // Only process if update is defined
                    if (!update) return null

                    const { powerBackupHours } = calculateStats(update)
                    const isEditing = update.id === editingId

                    return (
                      <TableRow key={update.id || Math.random().toString()}>
                        <TableCell>
                          {isEditing ? (
                            <Input
                              type="date"
                              value={formatDate(update.date || "")}
                              onChange={(e) => handleChange("date", e.target.value)}
                              className="w-32"
                            />
                          ) : (
                            formatDisplayDate(update.date || "")
                          )}
                        </TableCell>

                        <TableCell>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editData.starting_reading}
                              onChange={(e) => handleChange("starting_reading", e.target.value)}
                              className="w-24"
                              step="0.1"
                            />
                          ) : (
                            <>{(update.starting_reading || 0).toFixed(1)}</>
                          )}
                        </TableCell>

                        <TableCell>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editData.ending_reading}
                              onChange={(e) => handleChange("ending_reading", e.target.value)}
                              className="w-24"
                              step="0.1"
                            />
                          ) : (
                            <>{(update.ending_reading || 0).toFixed(1)}</>
                          )}
                        </TableCell>

                        <TableCell>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editData.fuel_in_generator_percentage}
                              onChange={(e) => handleChange("fuel_in_generator_percentage", e.target.value)}
                              className="w-24"
                            />
                          ) : (
                            <>{update.fuel_in_generator_percentage || 0}%</>
                          )}
                        </TableCell>

                        <TableCell>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editData.fuel_in_tank_liters}
                              onChange={(e) => handleChange("fuel_in_tank_liters", e.target.value)}
                              className="w-24"
                              step="0.1"
                            />
                          ) : (
                            <>{(update.fuel_in_tank_liters || 0).toFixed(1)} L</>
                          )}
                        </TableCell>

                        <TableCell>{powerBackupHours} hours</TableCell>

                        <TableCell>
                          {isEditing ? (
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleSave(update)}
                                disabled={isSubmitting}
                                className="h-8 w-8 p-0"
                                aria-label="Save changes"
                              >
                                <Save className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCancel}
                                disabled={isSubmitting}
                                className="h-8 w-8 p-0"
                                aria-label="Cancel editing"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEdit(update)}
                                className="h-8 w-8 p-0"
                                aria-label="Edit fuel update"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => confirmDelete(update.id)}
                                className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                aria-label="Delete fuel update"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this fuel update entry? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={isDeleting}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
