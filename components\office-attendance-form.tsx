"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { CalendarIcon, Check, Loader2, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { submitOfficeAttendance, checkOfficeAttendanceExists, type OfficeMember } from "@/app/actions/office-management"
import { getDefaultCheckInTime, getDefaultCheckOutTime, DEFAULT_WORKING_HOURS } from "@/lib/office-defaults"
import { ATTENDANCE_STATUS } from "@/lib/constants"

interface OfficeAttendanceFormProps {
  officeId: string
  officeName: string
  members?: OfficeMember[]
}

export function OfficeAttendanceForm({ officeId, officeName, members = [] }: OfficeAttendanceFormProps) {
  const [date, setDate] = useState<Date>(new Date())
  const [attendance, setAttendance] = useState<Record<string, boolean>>({})
  const [checkIn, setCheckIn] = useState<Record<string, string>>({})
  const [checkOut, setCheckOut] = useState<Record<string, string>>({})
  const [remarks, setRemarks] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [attendanceExists, setAttendanceExists] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  // Parse duty time to extract check-in and check-out times
  const parseDutyTime = (dutyTime: string | undefined): { checkIn: string; checkOut: string } => {
    if (!dutyTime) {
      return {
        checkIn: getDefaultCheckInTime(officeId),
        checkOut: getDefaultCheckOutTime(officeId),
      }
    }

    // Try to parse the duty time format like "9:15 - 18:45"
    const match = dutyTime.match(/(\d+:\d+)\s*-\s*(\d+:\d+)/)
    if (match && match.length === 3) {
      let [_, startTime, endTime] = match

      // Ensure proper format (HH:MM)
      if (startTime.length === 4) startTime = `0${startTime}` // Add leading zero if needed
      if (endTime.length === 4) endTime = `0${endTime}` // Add leading zero if needed

      return {
        checkIn: startTime,
        checkOut: endTime,
      }
    }

    // Fallback to defaults
    return {
      checkIn: getDefaultCheckInTime(officeId),
      checkOut: getDefaultCheckOutTime(officeId),
    }
  }

  // Update attendance state when members change
  useEffect(() => {
    if (members && members.length > 0) {
      const newAttendance: Record<string, boolean> = {}
      const newCheckIn: Record<string, string> = {}
      const newCheckOut: Record<string, string> = {}
      const newRemarks: Record<string, string> = {}

      members.forEach((member) => {
        newAttendance[member.id] = true

        // Parse duty time to get check-in and check-out times
        const { checkIn: startTime, checkOut: endTime } = parseDutyTime(member.duty_time)

        newCheckIn[member.id] = startTime
        newCheckOut[member.id] = endTime
        newRemarks[member.id] = member.remarks || ""
      })

      setAttendance(newAttendance)
      setCheckIn(newCheckIn)
      setCheckOut(newCheckOut)
      setRemarks(newRemarks)
    } else {
      setAttendance({})
      setCheckIn({})
      setCheckOut({})
      setRemarks({})
    }
    setIsLoading(false)
  }, [members, officeId])

  // Check if attendance already exists for this date
  useEffect(() => {
    const checkAttendance = async () => {
      if (date) {
        try {
          const exists = await checkOfficeAttendanceExists(officeId, format(date, "yyyy-MM-dd"))
          setAttendanceExists(exists)
        } catch (error) {
          console.error("Error checking attendance:", error)
          setAttendanceExists(false)
        }
      }
    }

    checkAttendance()
  }, [date, officeId])

  const handleAttendanceChange = (memberId: string, isPresent: boolean) => {
    setAttendance((prev) => ({
      ...prev,
      [memberId]: isPresent,
    }))
  }

  const handleCheckInChange = (memberId: string, time: string) => {
    setCheckIn((prev) => ({
      ...prev,
      [memberId]: time,
    }))
  }

  const handleCheckOutChange = (memberId: string, time: string) => {
    setCheckOut((prev) => ({
      ...prev,
      [memberId]: time,
    }))
  }

  const handleRemarksChange = (memberId: string, text: string) => {
    setRemarks((prev) => ({
      ...prev,
      [memberId]: text,
    }))
  }

  const calculateHoursWorked = (checkInTime: string, checkOutTime: string): number => {
    if (!checkInTime || !checkOutTime) return DEFAULT_WORKING_HOURS

    const [checkInHour, checkInMinute] = checkInTime.split(":").map(Number)
    const [checkOutHour, checkOutMinute] = checkOutTime.split(":").map(Number)

    const checkInMinutes = checkInHour * 60 + checkInMinute
    const checkOutMinutes = checkOutHour * 60 + checkOutMinute

    // If check-out is earlier than check-in, assume next day
    const diffMinutes =
      checkOutMinutes >= checkInMinutes ? checkOutMinutes - checkInMinutes : 24 * 60 - checkInMinutes + checkOutMinutes

    return Number.parseFloat((diffMinutes / 60).toFixed(2))
  }

  const handleSubmit = async () => {
    if (!members || members.length === 0) {
      toast({
        variant: "destructive",
        title: "No members",
        description: "There are no members to record attendance for.",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const records = members.map((member) => ({
        member_id: member.id,
        date: format(date, "yyyy-MM-dd"),
        status: attendance[member.id] ? ATTENDANCE_STATUS.PRESENT : ATTENDANCE_STATUS.ABSENT,
        check_in: attendance[member.id] ? checkIn[member.id] : null,
        check_out: attendance[member.id] ? checkOut[member.id] : null,
        hours_worked: attendance[member.id] ? calculateHoursWorked(checkIn[member.id], checkOut[member.id]) : 0,
        remarks: remarks[member.id] || "",
        office_location: officeId,
      }))

      const result = await submitOfficeAttendance(records)

      if (result.success) {
        toast({
          title: "Attendance Submitted",
          description: `Attendance for ${officeName} on ${format(date, "PPP")} has been recorded.`,
        })
        setAttendanceExists(true)
      } else {
        throw new Error(result.error || "Failed to submit attendance")
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "An error occurred while submitting attendance",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{officeName} Attendance</h3>
          <p className="text-sm text-muted-foreground">Record daily attendance for office members</p>
        </div>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className={cn("w-[240px] justify-start text-left font-normal")}>
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "PPP") : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar mode="single" selected={date} onSelect={(date) => date && setDate(date)} initialFocus />
          </PopoverContent>
        </Popover>
      </div>

      {attendanceExists && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4 text-amber-800">
          Attendance for this date has already been submitted. Submitting again will update the existing records.
        </div>
      )}

      {members.length === 0 ? (
        <div className="rounded-md border p-8 text-center">
          <p className="text-muted-foreground mb-4">No members found for this office.</p>
          <p className="text-sm">Please add members in the "Add/Remove Members" tab before submitting attendance.</p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Present</TableHead>
                <TableHead>Check-in</TableHead>
                <TableHead>Check-out</TableHead>
                <TableHead>Remarks</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">{member.name}</TableCell>
                  <TableCell>{member.role || "Staff"}</TableCell>
                  <TableCell>
                    <Checkbox
                      checked={attendance[member.id]}
                      onCheckedChange={(checked) => handleAttendanceChange(member.id, checked === true)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="time"
                        value={checkIn[member.id] || ""}
                        onChange={(e) => handleCheckInChange(member.id, e.target.value)}
                        disabled={!attendance[member.id]}
                        className="w-32"
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="time"
                        value={checkOut[member.id] || ""}
                        onChange={(e) => handleCheckOutChange(member.id, e.target.value)}
                        disabled={!attendance[member.id]}
                        className="w-32"
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Textarea
                      value={remarks[member.id] || ""}
                      onChange={(e) => handleRemarksChange(member.id, e.target.value)}
                      disabled={!attendance[member.id]}
                      placeholder={member.remarks || "Optional notes"}
                      className="h-10 min-h-0 resize-none"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      <Button onClick={handleSubmit} disabled={isSubmitting || members.length === 0} className="w-full">
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Submitting...
          </>
        ) : (
          <>
            <Check className="mr-2 h-4 w-4" />
            Submit Attendance
          </>
        )}
      </Button>
    </div>
  )
}
