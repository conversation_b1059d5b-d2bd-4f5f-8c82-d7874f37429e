#!/bin/bash

echo "🚀 SRSR Backend API Quick Start"
echo "================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Start PostgreSQL with Docker
echo "🐘 Starting PostgreSQL database..."
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env
fi

# Run database migration
echo "🗄️ Running database migration..."
npm run db:migrate

# Seed the database
echo "🌱 Seeding database with initial data..."
npm run db:seed

echo ""
echo "🎉 Setup complete!"
echo ""
echo "To start the API server:"
echo "  npm run dev"
echo ""
echo "To test the API:"
echo "  node test-api.js"
echo ""
echo "Database admin interface:"
echo "  http://localhost:8080 (adminer)"
echo "  Server: postgres"
echo "  Username: postgres"
echo "  Password: password"
echo "  Database: srsr_backend"
echo ""
echo "API documentation:"
echo "  http://localhost:3000/api"
