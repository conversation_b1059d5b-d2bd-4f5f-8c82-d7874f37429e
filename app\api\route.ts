import { NextResponse } from 'next/server'

export async function GET() {
  return NextResponse.json({
    name: 'SRSR Backend API',
    version: '1.0.0',
    description: 'Backend API for SRSR property management system',
    endpoints: {
      auth: {
        login: 'POST /api/auth/login',
        register: 'POST /api/auth/register',
        logout: 'POST /api/auth/logout',
      },
      sites: {
        list: 'GET /api/sites',
        create: 'POST /api/sites',
        get: 'GET /api/sites/{id}',
        update: 'PUT /api/sites/{id}',
        delete: 'DELETE /api/sites/{id}',
        members: 'GET /api/sites/{id}/members',
        addMember: 'POST /api/sites/{id}/members',
      },
      offices: {
        list: 'GET /api/offices',
        create: 'POST /api/offices',
        get: 'GET /api/offices/{id}',
        update: 'PUT /api/offices/{id}',
        delete: 'DELETE /api/offices/{id}',
        members: 'GET /api/offices/{id}/members',
        addMember: 'POST /api/offices/{id}/members',
      },
      maintenance: {
        list: 'GET /api/maintenance-issues',
        create: 'POST /api/maintenance-issues',
        get: 'GET /api/maintenance-issues/{id}',
        update: 'PUT /api/maintenance-issues/{id}',
        delete: 'DELETE /api/maintenance-issues/{id}',
      },
      generator: {
        list: 'GET /api/generator-fuel',
        create: 'POST /api/generator-fuel',
        get: 'GET /api/generator-fuel/{id}',
        update: 'PUT /api/generator-fuel/{id}',
        delete: 'DELETE /api/generator-fuel/{id}',
      },
      ott: {
        list: 'GET /api/ott-services',
        create: 'POST /api/ott-services',
        get: 'GET /api/ott-services/{id}',
        update: 'PUT /api/ott-services/{id}',
        delete: 'DELETE /api/ott-services/{id}',
      },
      uptime: {
        list: 'GET /api/uptime-reports',
        create: 'POST /api/uptime-reports',
        get: 'GET /api/uptime-reports/{id}',
        update: 'PUT /api/uptime-reports/{id}',
        delete: 'DELETE /api/uptime-reports/{id}',
      },
    },
    status: 'operational',
    timestamp: new Date().toISOString(),
  })
}
