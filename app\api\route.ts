import { NextResponse } from 'next/server'

export async function GET() {
  return NextResponse.json({
    name: 'SRSR Backend API',
    version: '1.0.0',
    description: 'Backend API for SRSR property management system',
    endpoints: {
      auth: {
        login: 'POST /api/auth/login',
        register: 'POST /api/auth/register',
        logout: 'POST /api/auth/logout',
      },
      sites: {
        list: 'GET /api/sites',
        create: 'POST /api/sites',
        get: 'GET /api/sites/{id}',
        update: 'PUT /api/sites/{id}',
        delete: 'DELETE /api/sites/{id}',
        members: 'GET /api/sites/{id}/members',
        addMember: 'POST /api/sites/{id}/members',
      },
      offices: {
        list: 'GET /api/offices',
        create: 'POST /api/offices',
        get: 'GET /api/offices/{id}',
        update: 'PUT /api/offices/{id}',
        delete: 'DELETE /api/offices/{id}',
        members: 'GET /api/offices/{id}/members',
        addMember: 'POST /api/offices/{id}/members',
      },
      maintenance: {
        list: 'GET /api/maintenance-issues?property_id={uuid}&status={status}&limit={number}',
        create: 'POST /api/maintenance-issues',
      },
      generator: {
        list: 'GET /api/generator-fuel?property_id={uuid}&limit={number}',
        create: 'POST /api/generator-fuel',
        get: 'GET /api/generator-fuel/{id}',
        update: 'PUT /api/generator-fuel/{id}',
        delete: 'DELETE /api/generator-fuel/{id}',
      },
      ott: {
        list: 'GET /api/ott-services?property_id={uuid}',
        create: 'POST /api/ott-services',
        get: 'GET /api/ott-services/{id}',
        update: 'PUT /api/ott-services/{id}',
        delete: 'DELETE /api/ott-services/{id}',
      },
      uptime: {
        list: 'GET /api/uptime-reports?property_id={uuid}&limit={number}',
        create: 'POST /api/uptime-reports',
      },
      attendance: {
        siteList: 'GET /api/attendance/site?site_id={uuid}&date={YYYY-MM-DD}&worker_id={uuid}&limit={number}',
        siteCreate: 'POST /api/attendance/site',
        officeList: 'GET /api/attendance/office?member_id={uuid}&date={YYYY-MM-DD}&office_location={string}&limit={number}',
        officeCreate: 'POST /api/attendance/office',
      },
    },
    database: {
      type: 'PostgreSQL',
      tables: [
        'users', 'sessions', 'roles', 'permissions',
        'sites', 'site_members', 'site_attendance',
        'offices', 'office_members', 'office_attendance',
        'maintenance_issues', 'escalation_matrix',
        'generator_fuel_updates', 'ott_services', 'uptime_reports',
        'threshold_config', 'function_process_matrix', 'diesel_additions'
      ]
    },
    status: 'operational',
    timestamp: new Date().toISOString(),
  })
}
