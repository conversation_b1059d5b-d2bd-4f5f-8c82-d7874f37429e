"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, Check, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { getUsers, getRoles, getUserRoles, updateUserStatus, updateUserRoles } from "@/app/actions/admin"
import type { User, Role } from "@/lib/auth"

export function UserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedUserRoles, setSelectedUserRoles] = useState<number[]>([])
  const [updatingStatus, setUpdatingStatus] = useState<Record<string, boolean>>({})
  const [updatingRoles, setUpdatingRoles] = useState(false)

  // Fetch users and roles
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        const usersData = await getUsers()
        const rolesData = await getRoles()
        setUsers(usersData)
        setRoles(rolesData)
      } catch (err) {
        setError("Failed to load users and roles")
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle status toggle
  const handleStatusToggle = async (userId: string, isActive: boolean) => {
    setUpdatingStatus((prev) => ({ ...prev, [userId]: true }))
    setError("")
    setSuccess("")

    try {
      const result = await updateUserStatus(userId, isActive)
      if (result.success) {
        setUsers(users.map((user) => (user.id === userId ? { ...user, is_active: isActive } : user)))
        setSuccess(`User status updated successfully`)
      } else {
        setError(result.error || "Failed to update user status")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setUpdatingStatus((prev) => ({ ...prev, [userId]: false }))
    }
  }

  // Open role dialog
  const openRoleDialog = async (user: User) => {
    setSelectedUser(user)
    setError("")

    try {
      const userRoles = await getUserRoles(user.id)
      setSelectedUserRoles(userRoles.map((role) => role.id))
    } catch (err) {
      setError("Failed to load user roles")
      console.error(err)
    }

    setIsRoleDialogOpen(true)
  }

  // Handle role checkbox change
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedUserRoles((prev) => [...prev, roleId])
    } else {
      setSelectedUserRoles((prev) => prev.filter((id) => id !== roleId))
    }
  }

  // Save user roles
  const saveUserRoles = async () => {
    if (!selectedUser) return

    setUpdatingRoles(true)
    setError("")
    setSuccess("")

    try {
      const result = await updateUserRoles(selectedUser.id, selectedUserRoles)
      if (result.success) {
        setIsRoleDialogOpen(false)
        setSuccess("User roles updated successfully")

        // Refresh user list to show updated roles
        const usersData = await getUsers()
        setUsers(usersData)
      } else {
        setError(result.error || "Failed to update user roles")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setUpdatingRoles(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 text-green-700">
          <Check className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>Manage user accounts and permissions</CardDescription>
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <div className="rounded-md bg-slate-50 p-8 text-center">
              <p className="text-slate-500">No users found</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Username</TableHead>
                    <TableHead>Full Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.username}</TableCell>
                      <TableCell>{user.full_name}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={user.is_active}
                            onCheckedChange={(checked) => handleStatusToggle(user.id, checked)}
                            disabled={updatingStatus[user.id]}
                          />
                          <span>
                            {user.is_active ? (
                              <Badge variant="outline" className="bg-green-50 text-green-700">
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-red-50 text-red-700">
                                Inactive
                              </Badge>
                            )}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>{user.role}</TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" onClick={() => openRoleDialog(user)}>
                          Manage Roles
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Role Management Dialog */}
      <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Manage User Roles</DialogTitle>
            <DialogDescription>{selectedUser && `Assign roles to ${selectedUser.username}`}</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {roles.map((role) => (
              <div key={role.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`role-${role.id}`}
                  checked={selectedUserRoles.includes(role.id)}
                  onCheckedChange={(checked) => handleRoleChange(role.id, checked === true)}
                />
                <Label htmlFor={`role-${role.id}`} className="flex-1">
                  {role.name}
                  <span className="block text-xs text-slate-500">{role.description}</span>
                </Label>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveUserRoles} disabled={updatingRoles}>
              {updatingRoles ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
