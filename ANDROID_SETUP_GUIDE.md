# Complete Android Setup Guide for SRSR Backend API

## Step-by-Step Setup

### 1. Start the Backend Server

First, make sure your backend is running and accessible from all network interfaces:

```bash
# Option 1: Quick setup (if you haven't set up the database yet)
quick-start.bat

# Option 2: Manual setup
# Start PostgreSQL
docker-compose up -d postgres

# Install dependencies (if not done)
npm install

# Run migrations
npm run db:migrate

# Seed database
npm run db:seed

# Start server (accessible from all interfaces)
npm run dev
```

The server should start on `http://0.0.0.0:3000` and show:
```
- Local:        http://localhost:3000
- Network:      http://YOUR_IP:3000
```

### 2. Test API Accessibility

Test if the API is accessible from Android emulator perspective:

```bash
node test-android-api.js
```

If successful, you should see:
```
✅ Root endpoint accessible from Android
✅ Admin login successful
✅ Sites endpoint accessible with authentication
```

### 3. Find Your Computer's IP Address

If `********` doesn't work, find your computer's IP:

**Windows:**
```cmd
ipconfig
```
Look for "IPv4 Address" (usually starts with 192.168.x.x or 10.x.x.x)

**Mac/Linux:**
```bash
ifconfig | grep inet
```

### 4. Configure Windows Firewall (Windows Users)

If you're on Windows, you may need to allow the port through the firewall:

1. Open Windows Defender Firewall
2. Click "Advanced settings"
3. Click "Inbound Rules" → "New Rule"
4. Select "Port" → "TCP" → "Specific local ports: 3000"
5. Allow the connection
6. Apply to all profiles
7. Name it "SRSR Backend API"

### 5. Android App Configuration

#### Option A: Using Android Emulator
```java
// Use this in your Android app
private static final String BASE_URL = "http://********:3000/api";
```

#### Option B: Using Physical Device
```java
// Replace with your computer's actual IP address
private static final String BASE_URL = "http://*************:3000/api";
```

## Quick Test URLs

Once your server is running, test these URLs:

### From your computer browser:
- `http://localhost:3000/api` - Should show API documentation
- `http://localhost:3000/api/sites` - Should show sites list

### From Android emulator browser:
- `http://********:3000/api` - Should show API documentation
- `http://********:3000/api/sites` - Should show sites list

### From physical Android device browser:
- `http://YOUR_IP:3000/api` - Should show API documentation

## Android App Implementation

### 1. Add Dependencies to build.gradle (Module: app)
```gradle
dependencies {
    // For HTTP requests
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // For JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Or use Retrofit (alternative)
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
}
```

### 2. Add Permissions to AndroidManifest.xml
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

<application
    android:usesCleartextTraffic="true"
    ... >
```

### 3. Simple API Test in Android

Create a simple test activity:

```java
public class ApiTestActivity extends AppCompatActivity {
    private static final String BASE_URL = "http://********:3000/api";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_api_test);
        
        // Test API connection
        testApiConnection();
    }
    
    private void testApiConnection() {
        new Thread(() -> {
            try {
                OkHttpClient client = new OkHttpClient();
                Request request = new Request.Builder()
                    .url(BASE_URL)
                    .build();
                
                Response response = client.newCall(request).execute();
                String responseBody = response.body().string();
                
                runOnUiThread(() -> {
                    if (response.isSuccessful()) {
                        Toast.makeText(this, "API Connected Successfully!", 
                            Toast.LENGTH_LONG).show();
                        Log.d("API_TEST", "Response: " + responseBody);
                    } else {
                        Toast.makeText(this, "API Connection Failed: " + response.code(), 
                            Toast.LENGTH_LONG).show();
                    }
                });
                
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Error: " + e.getMessage(), 
                        Toast.LENGTH_LONG).show();
                    Log.e("API_TEST", "Error", e);
                });
            }
        }).start();
    }
}
```

## Troubleshooting Common Issues

### 1. "Connection Refused" Error
**Solutions:**
- Ensure backend server is running: `npm run dev`
- Check server is bound to 0.0.0.0: Look for "Network: http://YOUR_IP:3000" in console
- Try using your computer's IP instead of ********

### 2. "Network Security Policy" Error (Android 9+)
**Solution:** Add to AndroidManifest.xml:
```xml
<application android:usesCleartextTraffic="true">
```

### 3. Firewall Blocking Connection
**Windows Solution:**
- Disable Windows Firewall temporarily to test
- Or add firewall rule for port 3000

**Mac Solution:**
```bash
sudo pfctl -d  # Disable firewall temporarily
```

### 4. Emulator Network Issues
**Solutions:**
- Restart Android emulator
- Try different emulator (Pixel 3a, API 30)
- Use physical device instead

### 5. CORS Errors
**Note:** CORS doesn't affect mobile apps, only web browsers

## Testing Checklist

✅ **Backend Setup:**
- [ ] PostgreSQL running (docker-compose up -d postgres)
- [ ] Database migrated (npm run db:migrate)
- [ ] Database seeded (npm run db:seed)
- [ ] Server running on 0.0.0.0:3000 (npm run dev)

✅ **Network Access:**
- [ ] API accessible from browser: http://localhost:3000/api
- [ ] API accessible from emulator: http://********:3000/api
- [ ] Firewall allows port 3000

✅ **Android App:**
- [ ] Internet permission added
- [ ] Clear text traffic allowed
- [ ] Correct base URL configured
- [ ] HTTP client implemented

## Sample API Calls for Testing

### 1. Get API Info
```
GET http://********:3000/api
```

### 2. Login
```
POST http://********:3000/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### 3. Get Sites (after login)
```
GET http://********:3000/api/sites
Cookie: session_token=YOUR_SESSION_TOKEN
```

### 4. Create Site
```
POST http://********:3000/api/sites
Content-Type: application/json
Cookie: session_token=YOUR_SESSION_TOKEN

{
  "name": "Test Site",
  "location": "Test Location"
}
```

## Next Steps

1. **Complete the backend setup** following this guide
2. **Test API accessibility** using the provided test script
3. **Implement Android HTTP client** using the provided code examples
4. **Test authentication flow** in your Android app
5. **Implement specific API calls** based on your app requirements

For detailed Android implementation examples, see `ANDROID_INTEGRATION.md`.
