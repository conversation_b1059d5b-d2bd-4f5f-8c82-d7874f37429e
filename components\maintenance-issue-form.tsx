"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Check } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createMaintenanceIssue, updateMaintenanceIssue, type MaintenanceIssue } from "@/app/actions/maintenance-issues"
import { RoleBasedUI } from "@/components/role-based-ui"

interface MaintenanceIssueFormProps {
  propertyId: string
  existingIssue?: MaintenanceIssue
  onSuccess?: () => void
}

export function MaintenanceIssueForm({ propertyId, existingIssue, onSuccess }: MaintenanceIssueFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    setSuccess(false)

    const formData = new FormData(e.currentTarget)
    formData.append("property_id", propertyId)

    try {
      const result = existingIssue ? await updateMaintenanceIssue(formData) : await createMaintenanceIssue(formData)

      if (result.success) {
        setSuccess(true)
        if (onSuccess) {
          onSuccess()
        } else {
          setTimeout(() => {
            router.push(`/dashboard/home/<USER>/maintenance?tab=issue-status`)
            router.refresh()
          }, 1500)
        }
      } else {
        setError(result.error || "An error occurred")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">{existingIssue ? "Edit Issue" : "Submit an Issue"}</CardTitle>
        <CardDescription>
          {existingIssue ? "Update the maintenance issue details" : "Report a new maintenance issue or request"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-700">
            <Check className="h-4 w-4" />
            <AlertDescription>
              {existingIssue ? "Issue updated successfully!" : "Issue submitted successfully!"}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {existingIssue && <input type="hidden" name="id" value={existingIssue.id} />}

          <div className="space-y-2">
            <Label htmlFor="issue-title">Issue Title</Label>
            <Input
              id="issue-title"
              name="issue_title"
              placeholder="Brief description of the issue"
              defaultValue={existingIssue?.issue_type || ""}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <select
              id="department"
              name="department"
              className="w-full rounded-md border border-input bg-background px-3 py-2"
              defaultValue={existingIssue?.category || ""}
              required
            >
              <option value="">Select a department</option>
              <option value="water">Water</option>
              <option value="electricity">Electricity</option>
              <option value="security">Security</option>
              <option value="internet">Internet</option>
              <option value="plumbing">Plumbing</option>
              <option value="electrical">Electrical</option>
              <option value="hvac">HVAC</option>
              <option value="structural">Structural</option>
              <option value="appliance">Appliance</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                name="start_date"
                type="date"
                defaultValue={existingIssue?.issue_date || new Date().toISOString().split("T")[0]}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end-date">Expected End Date</Label>
              <Input id="end-date" name="end_date" type="date" defaultValue={existingIssue?.resolution_date || ""} />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <select
              id="priority"
              name="priority"
              className="w-full rounded-md border border-input bg-background px-3 py-2"
              defaultValue={existingIssue?.priority || "Medium"}
              required
            >
              <option value="Low">Low - Not urgent</option>
              <option value="Medium">Medium - Needs attention soon</option>
              <option value="High">High - Urgent issue</option>
              <option value="Emergency">Emergency - Immediate attention required</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <select
              id="status"
              name="status"
              className="w-full rounded-md border border-input bg-background px-3 py-2"
              defaultValue={existingIssue?.status || "Open"}
              required
            >
              <option value="Open">Open</option>
              <option value="In Progress">In Progress</option>
              <option value="On Hold">On Hold</option>
              <option value="Resolved">Resolved</option>
              <option value="Closed">Closed</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reported-by">Reported By</Label>
            <Input
              id="reported-by"
              name="reported_by"
              placeholder="Your name"
              defaultValue={existingIssue?.reported_by || ""}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              name="remarks"
              placeholder="Please provide detailed information about the issue"
              className="min-h-[120px]"
              defaultValue={existingIssue?.remarks || ""}
            />
          </div>

          <RoleBasedUI
            roles={["admin", "maintenance_manager", "user"]}
            fallback={
              <Alert className="bg-yellow-50 text-yellow-700">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>You don't have permission to submit maintenance issues.</AlertDescription>
              </Alert>
            }
          >
            <div className="pt-2">
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? "Submitting..." : existingIssue ? "Update Issue" : "Submit Issue"}
              </Button>
            </div>
          </RoleBasedUI>
        </form>
      </CardContent>
    </Card>
  )
}
