import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createOttServiceSchema = z.object({
  property_id: z.string().uuid(),
  platform: z.string().min(1).max(100),
  plan_and_duration: z.string().min(1).max(255),
  username: z.string().min(1).max(255),
  password: z.string().min(1).max(255),
  next_recharge_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  current_status: z.string().min(1).max(50),
  fee: z.string().max(50).optional(),
  expiration_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
})

// GET /api/ott-services - Get all OTT services
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propertyId = searchParams.get('property_id')

    let query = `
      SELECT id, property_id, platform, plan_and_duration, username, password,
             next_recharge_date, current_status, fee, expiration_date,
             created_at, updated_at 
      FROM ott_services
    `
    const params: any[] = []

    if (propertyId) {
      query += ' WHERE property_id = $1'
      params.push(propertyId)
    }

    query += ' ORDER BY platform'

    const services = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: services,
    })

  } catch (error) {
    console.error('Error fetching OTT services:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch OTT services' },
      { status: 500 }
    )
  }
}

// POST /api/ott-services - Create a new OTT service
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createOttServiceSchema.parse(body)
    const {
      property_id,
      platform,
      plan_and_duration,
      username,
      password,
      next_recharge_date,
      current_status,
      fee,
      expiration_date,
    } = validatedData

    // Insert new OTT service
    const newService = await queryInsert(
      `INSERT INTO ott_services 
       (property_id, platform, plan_and_duration, username, password,
        next_recharge_date, current_status, fee, expiration_date) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
       RETURNING id, property_id, platform, plan_and_duration, username, password,
                 next_recharge_date, current_status, fee, expiration_date,
                 created_at, updated_at`,
      [
        property_id,
        platform,
        plan_and_duration,
        username,
        password,
        next_recharge_date,
        current_status,
        fee,
        expiration_date,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newService,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating OTT service:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create OTT service' },
      { status: 500 }
    )
  }
}
