import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createOttServiceSchema = z.object({
  property_id: z.string().min(1),
  service_name: z.string().min(1).max(255),
  provider: z.string().min(1).max(255),
  subscription_type: z.string().min(1).max(100),
  monthly_cost: z.number().min(0),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  expiry_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  status: z.string().max(50).default('Active'),
  auto_renewal: z.boolean().default(false),
  login_credentials: z.string().optional(),
  notes: z.string().optional(),
})

// GET /api/ott-services - Get all OTT services
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propertyId = searchParams.get('property_id')

    let query = `
      SELECT id, property_id, service_name, provider, subscription_type, monthly_cost,
             start_date, expiry_date, status, auto_renewal, login_credentials, notes,
             created_at, updated_at
      FROM ott_services
    `
    const params: any[] = []

    if (propertyId) {
      query += ' WHERE property_id = $1'
      params.push(propertyId)
    }

    query += ' ORDER BY platform'

    const services = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: services,
    })

  } catch (error) {
    console.error('Error fetching OTT services:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch OTT services' },
      { status: 500 }
    )
  }
}

// POST /api/ott-services - Create a new OTT service
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const validatedData = createOttServiceSchema.parse(body)
    const {
      property_id,
      service_name,
      provider,
      subscription_type,
      monthly_cost,
      start_date,
      expiry_date,
      status,
      auto_renewal,
      login_credentials,
      notes,
    } = validatedData

    // Insert new OTT service
    const newService = await queryInsert(
      `INSERT INTO ott_services
       (property_id, service_name, provider, subscription_type, monthly_cost,
        start_date, expiry_date, status, auto_renewal, login_credentials, notes)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
       RETURNING id, property_id, service_name, provider, subscription_type, monthly_cost,
                 start_date, expiry_date, status, auto_renewal, login_credentials, notes,
                 created_at, updated_at`,
      [
        property_id,
        service_name,
        provider,
        subscription_type,
        monthly_cost,
        start_date,
        expiry_date,
        status,
        auto_renewal,
        login_credentials,
        notes,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newService,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating OTT service:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create OTT service' },
      { status: 500 }
    )
  }
}
