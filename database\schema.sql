-- SRSR Backend Database Schema
-- PostgreSQL Database Schema for SRSR Backend API

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    is_active BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Roles table
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Permissions table
CREATE TABLE permissions (
    id SERIAL PRIMARY KEY,
    url_pattern VARCHAR(255) NOT NULL,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Sites table
CREATE TABLE sites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Site members table
CREATE TABLE site_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(20),
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    role VARCHAR(100),
    duty_time VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Offices table
CREATE TABLE offices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Office members table
CREATE TABLE office_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(20),
    office_location VARCHAR(255) NOT NULL,
    role VARCHAR(100),
    duty_time VARCHAR(100),
    remarks TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Site attendance table
CREATE TABLE site_attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    worker_id UUID NOT NULL REFERENCES site_members(id) ON DELETE CASCADE,
    worker_name VARCHAR(255) NOT NULL,
    worker_role VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('Present', 'Absent', 'Half Day', 'Leave')),
    hours_worked DECIMAL(4,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Office attendance table
CREATE TABLE office_attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID NOT NULL REFERENCES office_members(id) ON DELETE CASCADE,
    member_name VARCHAR(255),
    date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('Present', 'Absent', 'Half Day', 'Leave')),
    check_in TIME,
    check_out TIME,
    hours_worked DECIMAL(4,2),
    remarks TEXT,
    office_location VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Generator fuel updates table
CREATE TABLE generator_fuel_updates (
    id SERIAL PRIMARY KEY,
    property_id UUID NOT NULL,
    date DATE NOT NULL,
    starting_reading DECIMAL(10,2) NOT NULL,
    ending_reading DECIMAL(10,2) NOT NULL,
    fuel_in_generator_percentage DECIMAL(5,2) NOT NULL,
    fuel_in_tank_liters DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Maintenance issues table
CREATE TABLE maintenance_issues (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    issue_type VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    issue_date DATE NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Open',
    resolution_date DATE,
    remarks TEXT,
    reported_by VARCHAR(255) NOT NULL,
    assigned_to VARCHAR(255),
    priority VARCHAR(20) NOT NULL DEFAULT 'Medium',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Escalation matrix table
CREATE TABLE escalation_matrix (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    issue_id UUID NOT NULL REFERENCES maintenance_issues(id) ON DELETE CASCADE,
    escalation_level INTEGER NOT NULL,
    escalated_to VARCHAR(255) NOT NULL,
    resolved BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- OTT services table
CREATE TABLE ott_services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    platform VARCHAR(100) NOT NULL,
    plan_and_duration VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    next_recharge_date DATE NOT NULL,
    current_status VARCHAR(50) NOT NULL,
    fee VARCHAR(50),
    expiration_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Uptime reports table
CREATE TABLE uptime_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    date DATE NOT NULL,
    downtime_duration VARCHAR(50),
    uptime_percentage VARCHAR(10),
    event_time_ist VARCHAR(50),
    reason_for_disruption TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Threshold configuration table
CREATE TABLE threshold_config (
    id SERIAL PRIMARY KEY,
    functional_area VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    green_min DECIMAL(10,2),
    green_max DECIMAL(10,2),
    orange_min DECIMAL(10,2),
    orange_max DECIMAL(10,2),
    red_min DECIMAL(10,2),
    red_max DECIMAL(10,2),
    unit VARCHAR(20),
    description TEXT,
    property_type VARCHAR(50) NOT NULL DEFAULT 'all',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Function process matrix table
CREATE TABLE function_process_matrix (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    function_name VARCHAR(255) NOT NULL,
    sub_function VARCHAR(255) NOT NULL,
    input TEXT NOT NULL,
    process TEXT NOT NULL,
    output TEXT NOT NULL,
    threshold_limits TEXT NOT NULL,
    responsible_agent VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Diesel additions table
CREATE TABLE diesel_additions (
    id SERIAL PRIMARY KEY,
    property_id UUID NOT NULL,
    service_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    diesel_added DECIMAL(10,2) NOT NULL,
    added_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_token ON sessions(token);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_site_attendance_site_id ON site_attendance(site_id);
CREATE INDEX idx_site_attendance_date ON site_attendance(date);
CREATE INDEX idx_office_attendance_member_id ON office_attendance(member_id);
CREATE INDEX idx_office_attendance_date ON office_attendance(date);
CREATE INDEX idx_maintenance_issues_property_id ON maintenance_issues(property_id);
CREATE INDEX idx_maintenance_issues_status ON maintenance_issues(status);
CREATE INDEX idx_generator_fuel_property_id ON generator_fuel_updates(property_id);
CREATE INDEX idx_generator_fuel_date ON generator_fuel_updates(date);
CREATE INDEX idx_ott_services_property_id ON ott_services(property_id);
CREATE INDEX idx_uptime_reports_property_id ON uptime_reports(property_id);
CREATE INDEX idx_uptime_reports_date ON uptime_reports(date);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sites_updated_at BEFORE UPDATE ON sites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_site_members_updated_at BEFORE UPDATE ON site_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offices_updated_at BEFORE UPDATE ON offices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_office_members_updated_at BEFORE UPDATE ON office_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_generator_fuel_updated_at BEFORE UPDATE ON generator_fuel_updates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_issues_updated_at BEFORE UPDATE ON maintenance_issues FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ott_services_updated_at BEFORE UPDATE ON ott_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_uptime_reports_updated_at BEFORE UPDATE ON uptime_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_threshold_config_updated_at BEFORE UPDATE ON threshold_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_function_process_matrix_updated_at BEFORE UPDATE ON function_process_matrix FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
