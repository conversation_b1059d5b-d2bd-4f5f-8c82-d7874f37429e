-- SRSR Backend Database Schema
-- PostgreSQL Database Schema for SRSR Backend API
-- Aligned with existing database dump

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (in correct order to handle dependencies)
DROP TABLE IF EXISTS escalation_matrix CASCADE;
DROP TABLE IF EXISTS escalation_config CASCADE;
DROP TABLE IF EXISTS site_attendance CASCADE;
DROP TABLE IF EXISTS site_members CASCADE;
DROP TABLE IF EXISTS office_attendance CASCADE;
DROP TABLE IF EXISTS office_members CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS sessions CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS generator_fuel_updates CASCADE;
DROP TABLE IF EXISTS diesel_additions CASCADE;
DROP TABLE IF EXISTS maintenance_issues CASCADE;
DROP TABLE IF EXISTS ott_services CASCADE;
DROP TABLE IF EXISTS sites CASCADE;
DROP TABLE IF EXISTS offices CASCADE;
DROP TABLE IF EXISTS function_process_matrix CASCADE;
DROP TABLE IF EXISTS threshold_configurations CASCADE;
DROP TABLE IF EXISTS dashboard_widgets CASCADE;
DROP TABLE IF EXISTS network_disruption_history CASCADE;

-- 1. USERS TABLE
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. SESSIONS TABLE (for API authentication)
CREATE TABLE sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ROLES TABLE
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. USER_ROLES TABLE
CREATE TABLE user_roles (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, role_id)
);

-- 5. PERMISSIONS TABLE
CREATE TABLE permissions (
    id SERIAL PRIMARY KEY,
    url_pattern VARCHAR(255) NOT NULL,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. SITES TABLE
CREATE TABLE sites (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. OFFICES TABLE
CREATE TABLE offices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. GENERATOR FUEL UPDATES TABLE
CREATE TABLE generator_fuel_updates (
    id SERIAL PRIMARY KEY,
    property_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    starting_reading DECIMAL(10,2) NOT NULL,
    ending_reading DECIMAL(10,2) NOT NULL,
    fuel_in_generator_percentage INTEGER NOT NULL,
    fuel_in_tank_liters DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. DIESEL ADDITIONS TABLE
CREATE TABLE diesel_additions (
    id SERIAL PRIMARY KEY,
    property_id TEXT NOT NULL,
    service_id TEXT NOT NULL,
    date DATE NOT NULL,
    diesel_added NUMERIC(10,2) NOT NULL,
    added_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. MAINTENANCE ISSUES TABLE
CREATE TABLE maintenance_issues (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    property_id VARCHAR(255) NOT NULL,
    issue_type VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    issue_date DATE NOT NULL,
    status VARCHAR(50) DEFAULT 'Open',
    resolution_date DATE,
    remarks TEXT,
    reported_by VARCHAR(255) NOT NULL,
    assigned_to VARCHAR(255),
    priority VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. ESCALATION CONFIG TABLE
CREATE TABLE escalation_config (
    id SERIAL PRIMARY KEY,
    priority VARCHAR(50) NOT NULL,
    escalation_level INTEGER NOT NULL,
    days_to_escalate INTEGER NOT NULL,
    escalate_to VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(priority, escalation_level)
);

-- 12. ESCALATION MATRIX TABLE
CREATE TABLE escalation_matrix (
    id SERIAL PRIMARY KEY,
    issue_id UUID REFERENCES maintenance_issues(id) ON DELETE CASCADE,
    escalation_level INTEGER NOT NULL,
    escalated_to VARCHAR(255) NOT NULL,
    escalated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved BOOLEAN DEFAULT false
);

-- 13. OTT SERVICES TABLE
CREATE TABLE ott_services (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    property_id VARCHAR(255) NOT NULL,
    service_name VARCHAR(255) NOT NULL,
    provider VARCHAR(255) NOT NULL,
    subscription_type VARCHAR(100) NOT NULL,
    monthly_cost DECIMAL(10,2) NOT NULL,
    start_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    status VARCHAR(50) DEFAULT 'Active',
    auto_renewal BOOLEAN DEFAULT false,
    login_credentials TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 14. SITE MEMBERS TABLE
CREATE TABLE site_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(20) NOT NULL,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    role VARCHAR(100),
    duty_time VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 15. SITE ATTENDANCE TABLE
CREATE TABLE site_attendance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    worker_id VARCHAR(255) NOT NULL,
    worker_name VARCHAR(255) NOT NULL,
    worker_role VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    status VARCHAR(50) NOT NULL,
    hours_worked DECIMAL(4,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(site_id, worker_id, date)
);

-- 16. OFFICE MEMBERS TABLE
CREATE TABLE office_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(20) NOT NULL,
    office_location VARCHAR(255) NOT NULL,
    role VARCHAR(100),
    duty_time VARCHAR(100),
    remarks TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 17. OFFICE ATTENDANCE TABLE
CREATE TABLE office_attendance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    member_id UUID REFERENCES office_members(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    status VARCHAR(50) NOT NULL,
    check_in TIME,
    check_out TIME,
    hours_worked DECIMAL(4,2),
    remarks TEXT,
    office_location VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(member_id, date)
);

-- 18. FUNCTION PROCESS MATRIX TABLE
CREATE TABLE function_process_matrix (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    function_name TEXT NOT NULL,
    sub_function TEXT NOT NULL,
    input TEXT NOT NULL,
    process TEXT NOT NULL,
    output TEXT NOT NULL,
    threshold_limits TEXT NOT NULL,
    responsible_agent TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 19. THRESHOLD CONFIGURATIONS TABLE
CREATE TABLE threshold_configurations (
    id SERIAL PRIMARY KEY,
    functional_area VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    green_min DECIMAL(10,2),
    green_max DECIMAL(10,2),
    orange_min DECIMAL(10,2),
    orange_max DECIMAL(10,2),
    red_min DECIMAL(10,2),
    red_max DECIMAL(10,2),
    unit VARCHAR(20),
    description TEXT,
    property_type VARCHAR(50) DEFAULT 'all',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(functional_area, metric_name, property_type)
);

-- 20. DASHBOARD WIDGETS TABLE
CREATE TABLE dashboard_widgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    widget_type VARCHAR(100) NOT NULL,
    role_required VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    is_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 21. NETWORK DISRUPTION HISTORY TABLE
CREATE TABLE network_disruption_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    property_id VARCHAR(255) NOT NULL,
    date_period VARCHAR(255) NOT NULL,
    downtime_duration VARCHAR(255),
    uptime_percentage VARCHAR(10),
    event_time_ist TEXT,
    reason_for_disruption TEXT,
    uptime_numeric DECIMAL(5,2),
    downtime_minutes INTEGER,
    disruption_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX idx_generator_fuel_property_date ON generator_fuel_updates(property_id, date DESC);
CREATE INDEX idx_maintenance_property_status ON maintenance_issues(property_id, status);
CREATE INDEX idx_maintenance_date ON maintenance_issues(issue_date DESC);
CREATE INDEX idx_ott_property_status ON ott_services(property_id, status);
CREATE INDEX idx_ott_expiry ON ott_services(expiry_date);
CREATE INDEX idx_site_attendance_site_date ON site_attendance(site_id, date DESC);
CREATE INDEX idx_office_attendance_member_date ON office_attendance(member_id, date DESC);
CREATE INDEX idx_network_disruption_property_period ON network_disruption_history(property_id, date_period);
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_permissions_role ON permissions(role_id);
CREATE INDEX idx_sessions_token ON sessions(token);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_generator_fuel_updated_at BEFORE UPDATE ON generator_fuel_updates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_diesel_additions_updated_at BEFORE UPDATE ON diesel_additions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_issues_updated_at BEFORE UPDATE ON maintenance_issues FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_escalation_config_updated_at BEFORE UPDATE ON escalation_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ott_services_updated_at BEFORE UPDATE ON ott_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sites_updated_at BEFORE UPDATE ON sites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_site_members_updated_at BEFORE UPDATE ON site_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_site_attendance_updated_at BEFORE UPDATE ON site_attendance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offices_updated_at BEFORE UPDATE ON offices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_office_members_updated_at BEFORE UPDATE ON office_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_office_attendance_updated_at BEFORE UPDATE ON office_attendance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_function_process_matrix_updated_at BEFORE UPDATE ON function_process_matrix FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_threshold_configurations_updated_at BEFORE UPDATE ON threshold_configurations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dashboard_widgets_updated_at BEFORE UPDATE ON dashboard_widgets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_network_disruption_history_updated_at BEFORE UPDATE ON network_disruption_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
