import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createDashboardWidgetSchema = z.object({
  title: z.string().min(1).max(255),
  widget_type: z.string().min(1).max(100),
  role_required: z.string().max(100).optional(),
  display_order: z.number().min(0).default(0),
  is_enabled: z.boolean().default(true),
})

// GET /api/dashboard-widgets - Get all dashboard widgets
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const roleRequired = searchParams.get('role_required')
    const isEnabled = searchParams.get('is_enabled')

    let query = `
      SELECT id, title, widget_type, role_required, display_order, is_enabled,
             created_at, updated_at 
      FROM dashboard_widgets
    `
    const params: any[] = []
    const conditions: string[] = []

    if (roleRequired) {
      conditions.push(`role_required = $${params.length + 1}`)
      params.push(roleRequired)
    }

    if (isEnabled !== null && isEnabled !== undefined) {
      conditions.push(`is_enabled = $${params.length + 1}`)
      params.push(isEnabled === 'true')
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    query += ' ORDER BY display_order, title'

    const widgets = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: widgets,
    })

  } catch (error) {
    console.error('Error fetching dashboard widgets:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch dashboard widgets' },
      { status: 500 }
    )
  }
}

// POST /api/dashboard-widgets - Create a new dashboard widget
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createDashboardWidgetSchema.parse(body)
    const {
      title,
      widget_type,
      role_required,
      display_order,
      is_enabled,
    } = validatedData

    // Insert new dashboard widget
    const newWidget = await queryInsert(
      `INSERT INTO dashboard_widgets 
       (title, widget_type, role_required, display_order, is_enabled) 
       VALUES ($1, $2, $3, $4, $5) 
       RETURNING id, title, widget_type, role_required, display_order, is_enabled,
                 created_at, updated_at`,
      [
        title,
        widget_type,
        role_required,
        display_order,
        is_enabled,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newWidget,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating dashboard widget:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create dashboard widget' },
      { status: 500 }
    )
  }
}
