"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Check, Droplet } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createDieselAddition } from "@/app/actions/diesel-additions"
import { useToast } from "@/hooks/use-toast"

interface DieselAdditionFormProps {
  propertyId: string
  onSuccess?: () => void
}

export function DieselAdditionForm({ propertyId, onSuccess }: DieselAdditionFormProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(false)

    try {
      const formData = new FormData(e.currentTarget)
      formData.append("property_id", propertyId)

      // Validate form data
      const dieselAdded = formData.get("diesel_added")
      if (!dieselAdded || isNaN(Number(dieselAdded)) || Number(dieselAdded) <= 0) {
        setError("Please enter a valid diesel amount greater than zero")
        setLoading(false)
        return
      }

      const addedBy = formData.get("added_by")
      if (!addedBy || String(addedBy).trim() === "") {
        setError("Please enter who added the diesel")
        setLoading(false)
        return
      }

      const result = await createDieselAddition(formData)

      if (result && result.success) {
        setSuccess(true)
        toast({
          title: "Success",
          description: "Diesel addition recorded successfully!",
        })
        e.currentTarget.reset()
        if (onSuccess) {
          onSuccess()
        }
      } else {
        setError(result?.error || "Failed to add diesel record")
        toast({
          variant: "destructive",
          title: "Error",
          description: result?.error || "Failed to add diesel record",
        })
      }
    } catch (err) {
      console.error("Error submitting form:", err)
      setError("An unexpected error occurred. Please try again.")
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Droplet className="h-5 w-5" />
          Add Diesel
        </CardTitle>
        <CardDescription>Record a new diesel addition</CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-700">
            <Check className="h-4 w-4" />
            <AlertDescription>Diesel addition recorded successfully!</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <Input id="date" name="date" type="date" defaultValue={new Date().toISOString().split("T")[0]} required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="diesel-added">Diesel Added (in Ltrs)</Label>
            <Input
              id="diesel-added"
              name="diesel_added"
              type="number"
              step="0.1"
              min="0.1"
              placeholder="Enter amount in liters"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="added-by">Added By</Label>
            <Input id="added-by" name="added_by" type="text" placeholder="Enter name" required />
          </div>

          <div className="pt-2">
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Submitting..." : "Add Diesel"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
