import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createNetworkDisruptionSchema = z.object({
  property_id: z.string().min(1),
  date_period: z.string().min(1).max(255),
  downtime_duration: z.string().max(255).optional(),
  uptime_percentage: z.string().max(10).optional(),
  event_time_ist: z.string().optional(),
  reason_for_disruption: z.string().optional(),
  uptime_numeric: z.number().min(0).max(100).optional(),
  downtime_minutes: z.number().min(0).optional(),
  disruption_count: z.number().min(0).default(0),
})

// GET /api/network-disruption - Get all network disruption records
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propertyId = searchParams.get('property_id')
    const limit = searchParams.get('limit')

    let query = `
      SELECT id, property_id, date_period, downtime_duration, uptime_percentage,
             event_time_ist, reason_for_disruption, uptime_numeric, downtime_minutes,
             disruption_count, created_at, updated_at 
      FROM network_disruption_history
    `
    const params: any[] = []

    if (propertyId) {
      query += ' WHERE property_id = $1'
      params.push(propertyId)
    }

    query += ' ORDER BY date_period DESC, created_at DESC'

    if (limit) {
      const limitIndex = params.length + 1
      query += ` LIMIT $${limitIndex}`
      params.push(parseInt(limit))
    }

    const records = await queryMany(query, params)

    return NextResponse.json({
      success: true,
      data: records,
    })

  } catch (error) {
    console.error('Error fetching network disruption records:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch network disruption records' },
      { status: 500 }
    )
  }
}

// POST /api/network-disruption - Create a new network disruption record
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createNetworkDisruptionSchema.parse(body)
    const {
      property_id,
      date_period,
      downtime_duration,
      uptime_percentage,
      event_time_ist,
      reason_for_disruption,
      uptime_numeric,
      downtime_minutes,
      disruption_count,
    } = validatedData

    // Insert new network disruption record
    const newRecord = await queryInsert(
      `INSERT INTO network_disruption_history 
       (property_id, date_period, downtime_duration, uptime_percentage,
        event_time_ist, reason_for_disruption, uptime_numeric, downtime_minutes, disruption_count) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
       RETURNING id, property_id, date_period, downtime_duration, uptime_percentage,
                 event_time_ist, reason_for_disruption, uptime_numeric, downtime_minutes,
                 disruption_count, created_at, updated_at`,
      [
        property_id,
        date_period,
        downtime_duration,
        uptime_percentage,
        event_time_ist,
        reason_for_disruption,
        uptime_numeric,
        downtime_minutes,
        disruption_count,
      ]
    )

    return NextResponse.json({
      success: true,
      data: newRecord,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating network disruption record:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create network disruption record' },
      { status: 500 }
    )
  }
}
