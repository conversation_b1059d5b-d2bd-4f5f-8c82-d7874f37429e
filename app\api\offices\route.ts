import { NextRequest, NextResponse } from 'next/server'
import { queryMany, queryInsert } from '@/lib/database'
import { z } from 'zod'

const createOfficeSchema = z.object({
  name: z.string().min(1).max(255),
  location: z.string().min(1).max(255),
})

// GET /api/offices - Get all offices
export async function GET() {
  try {
    const offices = await queryMany(
      'SELECT id, name, location, created_at, updated_at FROM offices ORDER BY name'
    )

    return NextResponse.json({
      success: true,
      data: offices,
    })

  } catch (error) {
    console.error('Error fetching offices:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch offices' },
      { status: 500 }
    )
  }
}

// POST /api/offices - Create a new office
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = createOfficeSchema.parse(body)
    const { name, location } = validatedData

    // Insert new office
    const newOffice = await queryInsert(
      `INSERT INTO offices (name, location) 
       VALUES ($1, $2) 
       RETURNING id, name, location, created_at, updated_at`,
      [name, location]
    )

    return NextResponse.json({
      success: true,
      data: newOffice,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating office:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create office' },
      { status: 500 }
    )
  }
}
