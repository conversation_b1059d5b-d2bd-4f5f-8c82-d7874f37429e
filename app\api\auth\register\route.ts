import { NextRequest, NextResponse } from 'next/server'
import { db, queryOne, queryInsert } from '@/lib/database'
import { hashPassword } from '@/lib/auth'
import { z } from 'zod'

const registerSchema = z.object({
  username: z.string().min(3).max(50),
  password: z.string().min(6),
  fullName: z.string().min(1).max(255),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = registerSchema.parse(body)
    const { username, password, fullName } = validatedData

    // Check if username already exists
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE username = $1',
      [username]
    )

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'Username already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Insert new user
    const newUser = await queryInsert(
      `INSERT INTO users (username, password, full_name, email, role, is_active) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING id, username, full_name, email, role, is_active`,
      [username, hashedPassword, fullName, `${username}@example.com`, 'user', false]
    )

    return NextResponse.json({
      success: true,
      user: {
        id: newUser.id,
        username: newUser.username,
        fullName: newUser.full_name,
        email: newUser.email,
        role: newUser.role,
        isActive: newUser.is_active,
      },
    })

  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to register user' },
      { status: 500 }
    )
  }
}
