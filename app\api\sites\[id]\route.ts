import { NextRequest, NextResponse } from 'next/server'
import { queryOne, db } from '@/lib/database'
import { z } from 'zod'

const updateSiteSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  location: z.string().min(1).max(255).optional(),
})

// GET /api/sites/[id] - Get a specific site
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const site = await queryOne(
      'SELECT id, name, location, created_at, updated_at FROM sites WHERE id = $1',
      [id]
    )

    if (!site) {
      return NextResponse.json(
        { success: false, error: 'Site not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: site,
    })

  } catch (error) {
    console.error('Error fetching site:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch site' },
      { status: 500 }
    )
  }
}

// PUT /api/sites/[id] - Update a specific site
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    
    // Validate input
    const validatedData = updateSiteSchema.parse(body)
    
    // Check if site exists
    const existingSite = await queryOne('SELECT id FROM sites WHERE id = $1', [id])
    if (!existingSite) {
      return NextResponse.json(
        { success: false, error: 'Site not found' },
        { status: 404 }
      )
    }

    // Build update query dynamically
    const updateFields = []
    const updateValues = []
    let paramIndex = 1

    if (validatedData.name !== undefined) {
      updateFields.push(`name = $${paramIndex}`)
      updateValues.push(validatedData.name)
      paramIndex++
    }

    if (validatedData.location !== undefined) {
      updateFields.push(`location = $${paramIndex}`)
      updateValues.push(validatedData.location)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    updateValues.push(id) // Add id as the last parameter

    const updateQuery = `
      UPDATE sites 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramIndex}
      RETURNING id, name, location, created_at, updated_at
    `

    const result = await db.query(updateQuery, updateValues)
    const updatedSite = result.rows[0]

    return NextResponse.json({
      success: true,
      data: updatedSite,
    })

  } catch (error) {
    console.error('Error updating site:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update site' },
      { status: 500 }
    )
  }
}

// DELETE /api/sites/[id] - Delete a specific site
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if site exists
    const existingSite = await queryOne('SELECT id FROM sites WHERE id = $1', [id])
    if (!existingSite) {
      return NextResponse.json(
        { success: false, error: 'Site not found' },
        { status: 404 }
      )
    }

    // Delete site (this will cascade to related records)
    await db.query('DELETE FROM sites WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'Site deleted successfully',
    })

  } catch (error) {
    console.error('Error deleting site:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete site' },
      { status: 500 }
    )
  }
}
