// Simple test script to verify API endpoints
const BASE_URL = 'http://localhost:3000/api';

async function testAPI() {
  console.log('Testing SRSR Backend API...\n');

  try {
    // Test root endpoint
    console.log('1. Testing root endpoint...');
    const rootResponse = await fetch(`${BASE_URL}`);
    const rootData = await rootResponse.json();
    console.log('✅ Root endpoint working');
    console.log('API Name:', rootData.name);
    console.log('Version:', rootData.version);
    console.log('Status:', rootData.status);
    console.log('');

    // Test registration
    console.log('2. Testing user registration...');
    const registerResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'testpass123',
        fullName: 'Test User'
      })
    });

    const registerData = await registerResponse.json();
    if (registerData.success) {
      console.log('✅ User registration working');
      console.log('User ID:', registerData.user.id);
    } else {
      console.log('⚠️ Registration response:', registerData.error);
    }
    console.log('');

    // Test login
    console.log('3. Testing user login...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (loginData.success) {
      console.log('✅ User login working');
      console.log('User:', loginData.user.username);
      console.log('Role:', loginData.user.role);
    } else {
      console.log('❌ Login failed:', loginData.error);
    }
    console.log('');

    // Test sites endpoint
    console.log('4. Testing sites endpoint...');
    const sitesResponse = await fetch(`${BASE_URL}/sites`);
    const sitesData = await sitesResponse.json();
    if (sitesData.success) {
      console.log('✅ Sites endpoint working');
      console.log('Number of sites:', sitesData.data.length);
    } else {
      console.log('❌ Sites endpoint failed:', sitesData.error);
    }
    console.log('');

    // Test offices endpoint
    console.log('5. Testing offices endpoint...');
    const officesResponse = await fetch(`${BASE_URL}/offices`);
    const officesData = await officesResponse.json();
    if (officesData.success) {
      console.log('✅ Offices endpoint working');
      console.log('Number of offices:', officesData.data.length);
    } else {
      console.log('❌ Offices endpoint failed:', officesData.error);
    }
    console.log('');

    // Test maintenance issues endpoint
    console.log('6. Testing maintenance issues endpoint...');
    const maintenanceResponse = await fetch(`${BASE_URL}/maintenance-issues?limit=5`);
    const maintenanceData = await maintenanceResponse.json();
    if (maintenanceData.success) {
      console.log('✅ Maintenance issues endpoint working');
      console.log('Number of issues:', maintenanceData.data.length);
    } else {
      console.log('❌ Maintenance issues endpoint failed:', maintenanceData.error);
    }
    console.log('');

    // Test generator fuel endpoint
    console.log('7. Testing generator fuel endpoint...');
    const generatorResponse = await fetch(`${BASE_URL}/generator-fuel?limit=5`);
    const generatorData = await generatorResponse.json();
    if (generatorData.success) {
      console.log('✅ Generator fuel endpoint working');
      console.log('Number of records:', generatorData.data.length);
    } else {
      console.log('❌ Generator fuel endpoint failed:', generatorData.error);
    }
    console.log('');

    console.log('🎉 API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\nMake sure:');
    console.log('1. PostgreSQL is running');
    console.log('2. Database is created and migrated');
    console.log('3. API server is running on port 3000');
  }
}

// Run the test
testAPI();
