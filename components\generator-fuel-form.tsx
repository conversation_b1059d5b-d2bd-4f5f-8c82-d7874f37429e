"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Check, Droplet } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createGeneratorFuelUpdate } from "@/app/actions/generator-fuel"

interface GeneratorFuelFormProps {
  propertyId: string
  onSuccess?: () => void
}

export function GeneratorFuelForm({ propertyId, onSuccess }: GeneratorFuelFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    setSuccess(false)

    const formData = new FormData(e.currentTarget)
    formData.append("property_id", propertyId)

    try {
      const result = await createGeneratorFuelUpdate(formData)

      if (result.success) {
        setSuccess(true)
        e.currentTarget.reset()
        if (onSuccess) {
          onSuccess()
        }
      } else {
        setError(result.error || "An error occurred")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Droplet className="h-5 w-5" />
          Add Fuel Update
        </CardTitle>
        <CardDescription>Record a new generator fuel update</CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-700">
            <Check className="h-4 w-4" />
            <AlertDescription>Fuel update added successfully!</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <Input id="date" name="date" type="date" defaultValue={new Date().toISOString().split("T")[0]} required />
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="starting-reading">Starting Reading</Label>
              <Input
                id="starting-reading"
                name="starting_reading"
                type="number"
                step="0.1"
                placeholder="Enter meter reading"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="ending-reading">Ending Reading</Label>
              <Input
                id="ending-reading"
                name="ending_reading"
                type="number"
                step="0.1"
                placeholder="Enter meter reading"
                required
              />
            </div>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="fuel-in-generator">Fuel in Generator (%)</Label>
              <Input
                id="fuel-in-generator"
                name="fuel_in_generator_percentage"
                type="number"
                min="0"
                max="100"
                placeholder="Enter percentage"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fuel-in-tank">Fuel in Tank (liters)</Label>
              <Input
                id="fuel-in-tank"
                name="fuel_in_tank_liters"
                type="number"
                step="0.1"
                min="0"
                placeholder="Enter liters"
                required
              />
            </div>
          </div>

          <div className="pt-2">
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Submitting..." : "Add Fuel Update"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
